{"name": "backend", "packageManager": "yarn@4.9.2", "version": "1.0.0", "main": "src/index.ts", "private": true, "type": "module", "scripts": {"dev": "tsc-watch --noClear -p ./tsconfig.json --onSuccess \"yarn start\"", "build": "tsc ", "start": "tsc-alias && node --env-file=.env dist/index.js", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio"}, "dependencies": {"@genkit-ai/googleai": "^1.10.0", "@koa/router": "^13.1.0", "@prisma/client": "^6.8.2", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "genkit": "^1.10.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "koa": "^3.0.0", "mammoth": "^1.9.0", "openai": "^4.89.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/jsonwebtoken": "^9", "@types/koa": "^2.15.0", "@types/koa__router": "^12.0.4", "dotenv": "^16.4.7", "prisma": "^6.8.2", "tsc": "^2.0.4", "tsc-alias": "^1.8.16", "tsc-watch": "^7.1.1", "typescript": "^5.8.3"}}