import { Wire } from '@/wire';
import { User } from '@prisma/client';
import jwt from 'jsonwebtoken';

/**
 * Generates a JWT token for user authentication
 * @param user The user object to generate the token for
 * @returns A JWT token string
 */
export async function generateToken(wire: Wire, user: User): Promise<string> {
	const authConfig = wire.config.authConfig;

	const payload = {
		sub: user.id,
	};

	const options = {
		algorithm: 'HS256' as const,
		expiresIn: authConfig.jwtExpiresIn,
	};

	return jwt.sign(payload, authConfig.jwtSecret, options);
}

/**
 * Verifies a JWT token and returns the decoded payload with user ID
 * @param token The JWT token to verify
 * @returns The decoded token payload with user ID
 */
export async function verifyToken(
	wire: Wire,
	token: string
): Promise<{ id: string; sub: string; iat?: number; exp?: number }> {
	try {
		const authConfig = wire.config.authConfig;
		const decoded = jwt.verify(token, authConfig.jwtSecret, {
			algorithms: ['HS256'],
		}) as jwt.JwtPayload;

		return {
			id: decoded.sub as string,
			sub: decoded.sub as string,
			iat: decoded.iat,
			exp: decoded.exp,
		};
	} catch (error) {
		throw new Error('Invalid token');
	}
}
