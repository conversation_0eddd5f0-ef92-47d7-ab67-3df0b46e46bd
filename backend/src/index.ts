import Koa, { Context } from 'koa';
import Router from '@koa/router';
import { Wire } from './wire';
import {
	// Auth APIs
	logoutMiddleware,
	providerLoginMiddleware,
	developmentLoginMiddleware,
	usernamePasswordLoginMiddleware,
	logoutApiMiddleware,
	usernamePasswordLoginApiMiddleware,

	// Collection APIs
	deleteCollectionMiddleware,
	updateCollectionMiddleware,
	getCollectionMiddleware,
	getCollectionsMiddleware,
	createCollectionMiddleware,
	addWordToCollectionMiddleware,
	addTermToCollectionMiddleware,
	removeWordsFromCollectionMiddleware,
	addWordsToCollectionMiddleware,

	// Collection Stats APIs
	getCollectionStatsMiddleware,
	trackWordReviewMiddleware,
	getCollectionStatsApiMiddleware,
	getTodayStatsApiMiddleware,
	trackWordReviewApiMiddleware,
	trackQAPracticeSubmissionApiMiddleware,
	trackParagraphPracticeSubmissionApiMiddleware,

	// Feedback APIs
	submitFeedbackMiddleware,

	// Keyword APIs
	getUserKeywordsMiddleware,
	createKeywordMiddleware,
	updateKeywordMiddleware,
	deleteKeywordMiddleware,
	getKeywordMiddleware,

	// Last Seen Word APIs
	saveLastSeenWordMiddleware,

	// User APIs
	getCurrentUserMiddleware,
	getUserByIdMiddleware,

	// Word APIs
	getWordsByCollectionMiddleware,
	createLastSeenWordMiddleware,
	getWordsToReviewMiddleware,
	searchWordsMiddleware,
	getWordsByIdsMiddleware,
	bulkDeleteWordsFromCollectionMiddleware,

	// LLM APIs
	generateRandomWordsMiddleware,
	generateWordDetailsMiddleware,
	generateParagraphMiddleware,
	generateGrammarPracticeMiddleware,
	evaluateTranslationMiddleware,
	generateQuestionsMiddleware,
	generateParagraphWithQuestionsMiddleware,
	evaluateAnswersMiddleware,
} from './api';
import { authMiddleware, jsonBodyParserMiddleware } from './middleware';

const wire = new Wire();
const app = new Koa();
const router = new Router();

// Set up wire in context
app.context.wire = wire;

// JSON body parsing middleware
app.use(jsonBodyParserMiddleware);

// Health check route
router.get('/health', async (ctx: Context) => {
	ctx.body = { status: 'OK', message: 'Server is running' };
});

// Auth routes (public - no auth middleware)
router.post('/auth/logout', logoutMiddleware);
router.post('/auth/provider-login', providerLoginMiddleware);
router.post('/auth/development-login', developmentLoginMiddleware);
router.post('/auth/username-password-login', usernamePasswordLoginMiddleware);

// Alternative API routes for auth
router.post('/api/auth/logout', logoutApiMiddleware);
router.post('/api/auth/username-password-login', usernamePasswordLoginApiMiddleware);

// Protected routes (require authentication)
// User routes
router.get('/users/current', authMiddleware, getCurrentUserMiddleware);
router.get('/users/:id', authMiddleware, getUserByIdMiddleware);

// Collection routes
router.get('/collections', authMiddleware, getCollectionsMiddleware);
router.post('/collections', authMiddleware, createCollectionMiddleware);
router.get('/collections/:id', authMiddleware, getCollectionMiddleware);
router.put('/collections/:id', authMiddleware, updateCollectionMiddleware);
router.delete('/collections/:id', authMiddleware, deleteCollectionMiddleware);

// Collection word management routes
router.post('/collections/:id/words', authMiddleware, addWordToCollectionMiddleware);
router.post('/collections/:id/terms', authMiddleware, addTermToCollectionMiddleware);
router.post('/collections/:id/words/batch', authMiddleware, addWordsToCollectionMiddleware);
router.delete('/collections/:id/words', authMiddleware, removeWordsFromCollectionMiddleware);

// Collection stats routes
router.get('/collections/:id/stats', authMiddleware, getCollectionStatsMiddleware);
router.post('/collections/:id/track-review', authMiddleware, trackWordReviewMiddleware);

// Alternative API routes for collection stats
router.get('/api/collections/:id/stats', authMiddleware, getCollectionStatsApiMiddleware);
router.get('/api/collections/:id/stats/today', authMiddleware, getTodayStatsApiMiddleware);
router.post('/api/collections/:id/track-review', authMiddleware, trackWordReviewApiMiddleware);
router.post(
	'/api/collections/:id/track-qa-practice',
	authMiddleware,
	trackQAPracticeSubmissionApiMiddleware
);
router.post(
	'/api/collections/:id/track-paragraph-practice',
	authMiddleware,
	trackParagraphPracticeSubmissionApiMiddleware
);

// Keyword routes
router.get('/keywords', authMiddleware, getUserKeywordsMiddleware);
router.post('/keywords', authMiddleware, createKeywordMiddleware);
router.get('/keywords/:id', authMiddleware, getKeywordMiddleware);
router.put('/keywords/:id', authMiddleware, updateKeywordMiddleware);
router.delete('/keywords/:id', authMiddleware, deleteKeywordMiddleware);

// Last seen word routes
router.post('/last-seen-words', authMiddleware, saveLastSeenWordMiddleware);

// Feedback routes
router.post('/feedback', authMiddleware, submitFeedbackMiddleware);

// Word routes
router.get('/collections/:id/words', authMiddleware, getWordsByCollectionMiddleware);
router.post('/words/:id/last-seen', authMiddleware, createLastSeenWordMiddleware);
router.get('/collections/:id/words/review', authMiddleware, getWordsToReviewMiddleware);
router.get('/collections/:id/words/search', authMiddleware, searchWordsMiddleware);
router.post('/words/by-ids', authMiddleware, getWordsByIdsMiddleware);
router.delete(
	'/collections/:id/words/bulk',
	authMiddleware,
	bulkDeleteWordsFromCollectionMiddleware
);

// LLM routes
router.post('/llm/generate-words', authMiddleware, generateRandomWordsMiddleware);
router.post('/llm/generate-word-details', authMiddleware, generateWordDetailsMiddleware);
router.post('/llm/generate-paragraph', authMiddleware, generateParagraphMiddleware);
router.post('/llm/grammar-practice', authMiddleware, generateGrammarPracticeMiddleware);
router.post('/llm/evaluate-translation', authMiddleware, evaluateTranslationMiddleware);
router.post('/llm/generate-questions', authMiddleware, generateQuestionsMiddleware);
router.post(
	'/llm/generate-paragraph-with-questions',
	authMiddleware,
	generateParagraphWithQuestionsMiddleware
);
router.post('/llm/evaluate-answers', authMiddleware, evaluateAnswersMiddleware);

// Register router middleware
app.use(router.routes());
app.use(router.allowedMethods());

// Global error handler
app.on('error', (err, ctx) => {
	console.error('Server error:', err);
});

const port = wire.config.serverConfig.port;
app.listen(port, () => {
	console.log(`🚀 Server is running on port https://localhost:${port}`);
});
