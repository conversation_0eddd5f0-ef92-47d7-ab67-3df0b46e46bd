import {
	BaseRepository,
	BaseRepositoryImpl,
	CollectionRepository,
	CollectionRepositoryImpl,
	CollectionStatsRepository,
	CollectionStatsRepositoryImpl,
	KeywordRepository,
	KeywordRepositoryImpl,
	LastSeenWordRepository,
	LastSeenWordRepositoryImpl,
	UserRepository,
	UserRepositoryImpl,
	WordRepository,
	WordRepositoryImpl,
} from '@/repositories';
import {
	AuthService,
	AuthServiceImpl,
	CollectionService,
	CollectionServiceImpl,
	CollectionStatsService,
	CollectionStatsServiceImpl,
	FeedbackService,
	FeedbackServiceImpl,
	KeywordService,
	KeywordServiceImpl,
	LLMService,
	LastSeenWordService,
	LastSeenWordServiceImpl,
	UserService,
	UserServiceImpl,
	WordService,
	WordServiceImpl,
} from '@/services';
import { Feedback, PrismaClient } from '@prisma/client';
import { Config } from './config';

export class Wire {
	private _prismaClient: PrismaClient | null = null;

	private _config: Config | null = null;

	private _collectionRepository: CollectionRepository | null = null;
	private _keywordRepository: KeywordRepository | null = null;
	private _userRepository: UserRepository | null = null;
	private _wordRepository: WordRepository | null = null;
	private _lastSeenWordRepository: LastSeenWordRepository | null = null;
	private _feedbackRepository: BaseRepository<Feedback> | null = null;
	private _collectionStatsRepository: CollectionStatsRepository | null = null;

	private _userService: UserService | null = null;
	private _authService: AuthService | null = null;

	private _feedbackService: FeedbackService | null = null;
	private _lastSeenWordService: LastSeenWordService | null = null;
	private _llmService: LLMService | null = null;
	private _wordService: WordService | null = null;
	private _collectionService: CollectionService | null = null;
	private _keywordService: KeywordService | null = null;
	private _collectionStatsService: CollectionStatsService | null = null;

	// Prisma Client
	get prismaClient(): PrismaClient {
		if (!this._prismaClient) {
			this._prismaClient = new PrismaClient();
		}
		return this._prismaClient;
	}

	get config(): Config {
		if (!this._config) {
			this._config = new Config();
		}
		return this._config;
	}

	// Repositories
	get collectionRepository(): CollectionRepository {
		if (!this._collectionRepository) {
			this._collectionRepository = new CollectionRepositoryImpl(this.prismaClient);
		}
		return this._collectionRepository;
	}

	get keywordRepository(): KeywordRepository {
		if (!this._keywordRepository) {
			this._keywordRepository = new KeywordRepositoryImpl(this.prismaClient);
		}
		return this._keywordRepository;
	}

	get userRepository(): UserRepository {
		if (!this._userRepository) {
			this._userRepository = new UserRepositoryImpl(this.prismaClient);
		}
		return this._userRepository;
	}

	get wordRepository(): WordRepository {
		if (!this._wordRepository) {
			this._wordRepository = new WordRepositoryImpl(this.prismaClient);
		}
		return this._wordRepository;
	}

	get lastSeenWordRepository(): LastSeenWordRepository {
		if (!this._lastSeenWordRepository) {
			this._lastSeenWordRepository = new LastSeenWordRepositoryImpl(this.prismaClient);
		}
		return this._lastSeenWordRepository;
	}

	get feedbackRepository(): BaseRepository<Feedback> {
		if (!this._feedbackRepository) {
			this._feedbackRepository = new BaseRepositoryImpl<Feedback>(this.prismaClient.feedback);
		}
		return this._feedbackRepository;
	}

	get collectionStatsRepository(): CollectionStatsRepository {
		if (!this._collectionStatsRepository) {
			this._collectionStatsRepository = new CollectionStatsRepositoryImpl(this.prismaClient);
		}
		return this._collectionStatsRepository;
	}

	// Services
	get userService(): UserService {
		if (!this._userService) {
			this._userService = new UserServiceImpl(this);
		}
		return this._userService;
	}

	get authService(): AuthService {
		if (!this._authService) {
			this._authService = new AuthServiceImpl(this);
		}
		return this._authService;
	}

	get feedbackService(): FeedbackService {
		if (!this._feedbackService) {
			this._feedbackService = new FeedbackServiceImpl(this);
		}
		return this._feedbackService;
	}

	get lastSeenWordService(): LastSeenWordService {
		if (!this._lastSeenWordService) {
			this._lastSeenWordService = new LastSeenWordServiceImpl(this);
		}
		return this._lastSeenWordService;
	}

	get llmService(): LLMService {
		if (!this._llmService) {
			this._llmService = new LLMService(this);
		}
		return this._llmService;
	}

	get wordService(): WordService {
		if (!this._wordService) {
			this._wordService = new WordServiceImpl(this);
		}
		return this._wordService;
	}

	get collectionService(): CollectionService {
		if (!this._collectionService) {
			this._collectionService = new CollectionServiceImpl(this);
		}
		return this._collectionService;
	}

	get keywordService(): KeywordService {
		if (!this._keywordService) {
			this._keywordService = new KeywordServiceImpl(this);
		}
		return this._keywordService;
	}

	get collectionStatsService(): CollectionStatsService {
		if (!this._collectionStatsService) {
			this._collectionStatsService = new CollectionStatsServiceImpl(this);
		}
		return this._collectionStatsService;
	}
}
