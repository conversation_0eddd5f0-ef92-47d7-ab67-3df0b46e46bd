'use server';

import { Context, Next } from 'koa';

/**
 * Middleware to parse JSON request bodies
 * @param ctx - Koa context
 * @param next - Next middleware function
 */
export async function jsonBodyParserMiddleware(ctx: Context, next: Next) {
	if (ctx.request.type === 'application/json') {
		try {
			const body = await new Promise<string>((resolve, reject) => {
				let data = '';
				ctx.req.on('data', (chunk) => {
					data += chunk;
				});
				ctx.req.on('end', () => {
					resolve(data);
				});
				ctx.req.on('error', reject);
			});
			(ctx.request as any).body = JSON.parse(body);
		} catch (error) {
			ctx.status = 400;
			ctx.body = { error: 'Invalid JSON' };
			return;
		}
	}
	await next();
}
