import { UnauthorizedError } from '@/errors';
import { KoaContext } from '@/types/koa';
import { verifyToken } from '@/utils';
import { Next } from 'koa';

export async function authMiddleware(ctx: KoaContext, next: Next): Promise<void> {
	const authConfig = ctx.wire.config.authConfig;

	const token = ctx.cookies.get(authConfig.jwtCookieName);
	if (!token) throw new UnauthorizedError('No authentication token provided');

	const decoded = await verifyToken(ctx.wire, token);
	if (!decoded?.id) throw new UnauthorizedError('Invalid token');

	const user = await ctx.wire.userService.getUserById(decoded.id);
	if (!user) throw new UnauthorizedError('User not found');
	ctx.state.user = user;

	await next();
}
