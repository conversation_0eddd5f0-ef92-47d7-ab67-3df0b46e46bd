import { Next } from 'koa';
import { AuthKoaContext } from '@/types/koa';
import { ValidationError } from '@/errors';
import { DailyStats } from '@/services/collection-stats.service';
import { Wire } from '@/wire';

/**
 * Get collection statistics API function
 * @param wire - The wire dependency injection container
 * @param collectionId - The collection ID
 * @param userId - The user ID
 * @param days - Number of days to fetch stats for (optional)
 * @returns Promise resolving to array of daily stats
 * @throws {ValidationError} If collection ID or user ID is missing
 */
export async function getCollectionStatsApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	days?: number
): Promise<DailyStats[]> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!userId) {
		throw new ValidationError('User ID is required');
	}

	try {
		const collectionStatsService = wire.collectionStatsService;
		return await collectionStatsService.getStatsForCollection(collectionId, userId, days);
	} catch (error) {
		throw new Error(
			`Failed to get collection stats: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

/**
 * Get today's statistics API function
 * @param wire - The wire dependency injection container
 * @param collectionId - The collection ID
 * @param userId - The user ID
 * @returns Promise resolving to today's stats
 * @throws {ValidationError} If collection ID or user ID is missing
 */
export async function getTodayStatsApi(
	wire: Wire,
	collectionId: string,
	userId: string
): Promise<DailyStats> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!userId) {
		throw new ValidationError('User ID is required');
	}

	try {
		const collectionStatsService = wire.collectionStatsService;
		return await collectionStatsService.getTodayStats(collectionId, userId);
	} catch (error) {
		throw new Error(
			`Failed to get today's stats: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

/**
 * Track word review API function
 * @param wire - The wire dependency injection container
 * @param collectionId - The collection ID
 * @param userId - The user ID
 * @param count - Number of reviews to track (optional, defaults to 1)
 * @throws {ValidationError} If collection ID or user ID is missing
 */
export async function trackWordReviewApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	count?: number
): Promise<void> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!userId) {
		throw new ValidationError('User ID is required');
	}

	try {
		const collectionStatsService = wire.collectionStatsService;
		await collectionStatsService.trackWordReview(collectionId, userId, count);
	} catch (error) {
		throw new Error(
			`Failed to track word review: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

/**
 * Track QA practice submission API function
 * @param wire - The wire dependency injection container
 * @param collectionId - The collection ID
 * @param userId - The user ID
 * @param count - Number of submissions to track (optional, defaults to 1)
 * @throws {ValidationError} If collection ID or user ID is missing
 */
export async function trackQAPracticeSubmissionApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	count?: number
): Promise<void> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!userId) {
		throw new ValidationError('User ID is required');
	}

	try {
		const collectionStatsService = wire.collectionStatsService;
		await collectionStatsService.trackQAPracticeSubmission(collectionId, userId, count);
	} catch (error) {
		throw new Error(
			`Failed to track QA practice: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

/**
 * Track paragraph practice submission API function
 * @param wire - The wire dependency injection container
 * @param collectionId - The collection ID
 * @param userId - The user ID
 * @param count - Number of submissions to track (optional, defaults to 1)
 * @throws {ValidationError} If collection ID or user ID is missing
 */
export async function trackParagraphPracticeSubmissionApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	count?: number
): Promise<void> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!userId) {
		throw new ValidationError('User ID is required');
	}

	try {
		const collectionStatsService = wire.collectionStatsService;
		await collectionStatsService.trackParagraphPracticeSubmission(collectionId, userId, count);
	} catch (error) {
		throw new Error(
			`Failed to track paragraph practice: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

/**
 * Get collection statistics middleware
 * GET /collections/:id/stats
 */
export async function getCollectionStatsMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}

	const collectionStatsService = ctx.wire.collectionStatsService;
	const stats = await collectionStatsService.getStatsForCollection(collectionId, userId);

	ctx.status = 200;
	ctx.body = stats;
}

/**
 * Track collection activity middleware
 * POST /collections/:id/stats/track
 */
export async function trackWordReviewMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const data = (ctx.request as any).body as { count?: number };

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}

	const collectionStatsService = ctx.wire.collectionStatsService;
	await collectionStatsService.trackWordReview(collectionId, userId, data.count);

	ctx.status = 200;
	ctx.body = { success: true };
}

/**
 * Get collection statistics API middleware for /api/collections/:id/stats route
 */
export async function getCollectionStatsApiMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const days = ctx.query.days ? parseInt(ctx.query.days as string) : undefined;

	const stats = await getCollectionStatsApi(ctx.wire, collectionId, userId, days);
	ctx.status = 200;
	ctx.body = stats;
}

/**
 * Get today's collection statistics API middleware for /api/collections/:id/stats/today route
 */
export async function getTodayStatsApiMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;

	const stats = await getTodayStatsApi(ctx.wire, collectionId, userId);
	ctx.status = 200;
	ctx.body = stats;
}

/**
 * Track word review API middleware for /api/collections/:id/track-review route
 */
export async function trackWordReviewApiMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const { count } = (ctx.request as any).body as { count?: number };

	await trackWordReviewApi(ctx.wire, collectionId, userId, count);
	ctx.status = 200;
	ctx.body = { success: true };
}

/**
 * Track QA practice submission API middleware for /api/collections/:id/track-qa-practice route
 */
export async function trackQAPracticeSubmissionApiMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const { count } = (ctx.request as any).body as { count?: number };

	await trackQAPracticeSubmissionApi(ctx.wire, collectionId, userId, count);
	ctx.status = 200;
	ctx.body = { success: true };
}

/**
 * Track paragraph practice submission API middleware for /api/collections/:id/track-paragraph-practice route
 */
export async function trackParagraphPracticeSubmissionApiMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const { count } = (ctx.request as any).body as { count?: number };

	await trackParagraphPracticeSubmissionApi(ctx.wire, collectionId, userId, count);
	ctx.status = 200;
	ctx.body = { success: true };
}
