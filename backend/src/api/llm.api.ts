import { ValidationError } from '@/errors';
import { RandomWord } from '@/models';
import {
	AnswerEvaluationResult,
	EvaluateAnswersParams,
	EvaluateTranslationParams,
	GenerateParagraphWithQuestionsParams,
	GenerateQuestionsParams,
	GrammarPracticeParams,
	GrammarPracticeResultItem,
	ParagraphWithQuestionsResult,
	TranslationEvaluationResult,
} from '@/services';
import { AuthKoaContext } from '@/types/koa';
import { Difficulty, Language, Word } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';
import { z, ZodError } from 'zod';
import { Wire } from '@/wire';

// Base schemas for reusable validation
const languageSchema = z.nativeEnum(Language);
const difficultySchema = z.nativeEnum(Difficulty);
const nonEmptyStringSchema = z.string().min(1, 'Must be a non-empty string');
const nonEmptyStringArraySchema = z.array(nonEmptyStringSchema).min(1, 'Array cannot be empty');

// Generate Random Words validation
const generateRandomWordsSchema = z.object({
	keywordTerms: nonEmptyStringArraySchema,
	maxTerms: z.number().int().min(1).max(50, 'Max terms must be between 1 and 50'),
	excludeCollectionIds: z.array(nonEmptyStringSchema).optional().default([]),
	source_language: languageSchema,
	target_language: languageSchema,
});

// Generate Word Details validation
const generateWordDetailsSchema = z.object({
	terms: nonEmptyStringArraySchema.max(10, 'Maximum of 10 terms allowed'),
	source_language: languageSchema,
	target_language: languageSchema,
});

// Generate Paragraph validation
const generateParagraphSchema = z.object({
	keywords: nonEmptyStringArraySchema,
	language: languageSchema,
	difficulty: difficultySchema,
	count: z.number().int().min(1).max(5, 'Count must be between 1 and 5'),
	sentenceCount: z.number().int().min(1).optional(),
});

// Grammar Practice validation
const grammarPracticeSchema = z
	.object({
		keywords: nonEmptyStringArraySchema,
		language: languageSchema,
		source_language: languageSchema,
		target_language: languageSchema,
		difficulty: difficultySchema,
		count: z.number().int().min(1).max(5, 'Count must be between 1 and 5'),
		sentenceCount: z.number().int().min(1).optional(),
		errorDensity: z.enum(['low', 'medium', 'high']).optional(),
	})
	.refine((data) => data.source_language !== data.target_language, {
		message: 'Source and target languages must be different',
		path: ['target_language'],
	});

// Evaluate Translation validation
const evaluateTranslationSchema = z
	.object({
		original_text: nonEmptyStringSchema,
		translated_text: nonEmptyStringSchema,
		source_language: languageSchema,
		target_language: languageSchema,
	})
	.refine((data) => data.source_language !== data.target_language, {
		message: 'Source and target languages must be different',
		path: ['target_language'],
	});

// Generate Questions validation
const generateQuestionsSchema = z.object({
	paragraph: nonEmptyStringSchema,
	language: languageSchema,
	questionCount: z.number().int().min(1).max(10, 'Question count must be between 1 and 10'),
});

// Generate Paragraph with Questions validation
const generateParagraphWithQuestionsSchema = z.object({
	keywords: nonEmptyStringArraySchema,
	language: languageSchema,
	difficulty: difficultySchema,
	sentenceCount: z.number().int().min(1).optional(),
	questionCount: z.number().int().min(1).max(10, 'Question count must be between 1 and 10'),
});

// Evaluate Answers validation
const evaluateAnswersSchema = z
	.object({
		paragraph: nonEmptyStringSchema,
		questions: nonEmptyStringArraySchema,
		answers: z.array(z.string()), // Allow empty strings for skipped answers
		qna_language: languageSchema,
		feedback_native_language: languageSchema,
	})
	.refine((data) => data.questions.length === data.answers.length, {
		message: 'The number of questions and answers must match',
		path: ['answers'],
	});

// Type exports for the validated data
export type GenerateRandomWordsInput = z.infer<typeof generateRandomWordsSchema>;
export type GenerateWordDetailsInput = z.infer<typeof generateWordDetailsSchema>;
export type GenerateParagraphInput = z.infer<typeof generateParagraphSchema>;
export type GrammarPracticeInput = z.infer<typeof grammarPracticeSchema>;
export type EvaluateTranslationInput = z.infer<typeof evaluateTranslationSchema>;
export type GenerateQuestionsInput = z.infer<typeof generateQuestionsSchema>;
export type GenerateParagraphWithQuestionsInput = z.infer<
	typeof generateParagraphWithQuestionsSchema
>;
export type EvaluateAnswersInput = z.infer<typeof evaluateAnswersSchema>;

/**
 * Converts Zod validation errors to ValidationError
 */
function handleValidationError(error: ZodError): never {
	const message = error.errors.map((err) => `${err.path.join('.')}: ${err.message}`).join('; ');
	throw new ValidationError(message);
}

/**
 * Generates random terms (words) for the authenticated user based on keywords,
 * with options to exclude terms from certain collections.
 * Uses local JSON file caching in development mode for better performance.
 * @param wire - The wire dependency injection container
 * @param keywordTerms - Array of keyword strings to base generation on.
 * @param maxTerms - Maximum number of terms to generate (1-50).
 * @param excludeCollectionIds - Optional array of collection IDs whose terms should be excluded.
 * @param source_language - The user's native language.
 * @param target_language - The language being learned.
 * @returns A promise that resolves to an array of RandomWord objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If keywordTerms are missing/empty or maxTerms is out of range, or languages are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateRandomWordsApi(
	wire: Wire,
	keywordTerms: string[],
	maxTerms: number,
	excludeCollectionIds: string[] = [],
	source_language: Language, // Add source_language
	target_language: Language // Add target_language
): Promise<RandomWord[]> {
	// Validate input using Zod
	try {
		generateRandomWordsSchema.parse({
			keywordTerms,
			maxTerms,
			excludeCollectionIds,
			source_language,
			target_language,
		});
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	// Generate cache filename based on parameters
	const sortedKeywords = [...keywordTerms].sort();
	const keywordsStr = sortedKeywords
		.map((keyword) => keyword.replace(/[^a-zA-Z0-9_]+/g, '_'))
		.join('_');
	const sortedExcludeIds = [...excludeCollectionIds].sort();
	const excludeIdsStr =
		sortedExcludeIds.length > 0 ? `_exclude${sortedExcludeIds.join('_')}` : '';
	const cacheDir = path.join(process.cwd(), 'cache');
	const filePath = path.join(
		cacheDir,
		`randomWords_${keywordsStr}_max${maxTerms}${excludeIdsStr}_${source_language}_${target_language}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as RandomWord[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
			// Fall through to generate if file not found or other read error
		}
	}

	const llmService = wire.llmService;
	try {
		const words = await llmService.generateRandomTerms({
			userId: 'api-user', // This should be passed from the middleware
			keywordTerms,
			excludesTerms: [], // Default empty array
			maxTerms,
			excludeCollectionIds,
			source_language,
			target_language,
		});

		// Cache the result in development mode
		if (process.env.NODE_ENV === 'development') {
			try {
				await fs.mkdir(cacheDir, { recursive: true });
				await fs.writeFile(filePath, JSON.stringify(words, null, 2));
				console.log(`Development mode: Cached random words to ${filePath}`);
			} catch (err: any) {
				console.warn(
					`Development mode: Could not cache random words to ${filePath}. Error: ${err.message}`
				);
			}
		}

		return words;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(
			`Failed to generate random words for keywords [${keywordTerms.join(
				', '
			)}], maxTerms ${maxTerms}:`,
			error
		);
		throw new Error('Failed to generate random words. Please try again.');
	}
}

/**
 * Generates detailed word information for given terms.
 * @param wire - The wire dependency injection container
 * @param terms - Array of terms to generate details for (max 10).
 * @param source_language - The user's native language.
 * @param target_language - The language being learned.
 * @returns A promise that resolves to an array of Word objects with detailed information.
 * @throws {ValidationError} If terms are missing/empty or exceed max length, or languages are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateWordDetailsApi(
	wire: Wire,
	terms: string[],
	source_language: Language, // Add source_language
	target_language: Language // Add target_language
): Promise<Word[]> {
	// Validate input using Zod
	try {
		generateWordDetailsSchema.parse({
			terms,
			source_language,
			target_language,
		});
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const words = await llmService.generateWordDetails(terms, source_language, target_language);
		return words;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(`Failed to generate word details for terms [${terms.join(', ')}]:`, error);
		throw new Error('Failed to generate word details. Please try again.');
	}
}

/**
 * Generates paragraphs based on keywords and difficulty.
 * @param wire - The wire dependency injection container
 * @param keywords - Array of keywords to include in paragraphs.
 * @param language - Language of the paragraphs.
 * @param difficulty - Difficulty level of the paragraphs.
 * @param count - Number of paragraphs to generate (1-5).
 * @param sentenceCount - Optional number of sentences per paragraph.
 * @returns A promise that resolves to an array of paragraph strings.
 * @throws {ValidationError} If parameters are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateParagraphApi(
	wire: Wire,
	keywords: string[],
	language: Language,
	difficulty: Difficulty,
	count: number,
	sentenceCount?: number
): Promise<string[]> {
	// Validate input using Zod
	try {
		generateParagraphSchema.parse({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
		});
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const paragraphs = await llmService.generateParagraph({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
		});
		return paragraphs;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(
			`Failed to generate paragraphs for keywords [${keywords.join(', ')}]:`,
			error
		);
		throw new Error('Failed to generate paragraphs. Please try again.');
	}
}

/**
 * Generates grammar practice exercises based on parameters.
 * @param wire - The wire dependency injection container
 * @param params - Grammar practice parameters.
 * @returns A promise that resolves to an array of grammar practice result items.
 * @throws {ValidationError} If parameters are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateGrammarPracticeApi(
	wire: Wire,
	params: GrammarPracticeParams
): Promise<GrammarPracticeResultItem[]> {
	// Validate input using Zod
	try {
		grammarPracticeSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const exercises = await llmService.generateGrammarPractice(params);
		return exercises;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(`Failed to generate grammar practice exercises:`, error);
		throw new Error('Failed to generate grammar practice exercises. Please try again.');
	}
}

/**
 * Evaluates a translation for accuracy and provides feedback.
 * @param wire - The wire dependency injection container
 * @param params - Translation evaluation parameters.
 * @returns A promise that resolves to a translation evaluation result.
 * @throws {ValidationError} If parameters are invalid.
 * @throws {Error} If evaluation fails for other reasons.
 */
export async function evaluateTranslationApi(
	wire: Wire,
	params: EvaluateTranslationParams
): Promise<TranslationEvaluationResult> {
	// Validate input using Zod
	try {
		evaluateTranslationSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const evaluation = await llmService.evaluateTranslation(params);
		return evaluation;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(`Failed to evaluate translation:`, error);
		throw new Error('Failed to evaluate translation. Please try again.');
	}
}

/**
 * Generates questions based on a paragraph.
 * @param wire - The wire dependency injection container
 * @param params - Question generation parameters.
 * @returns A promise that resolves to an array of question strings.
 * @throws {ValidationError} If parameters are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateQuestionsApi(
	wire: Wire,
	params: GenerateQuestionsParams
): Promise<string[]> {
	// Validate input using Zod
	try {
		generateQuestionsSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const questions = await llmService.generateQuestions(params);
		return questions;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(`Failed to generate questions:`, error);
		throw new Error('Failed to generate questions. Please try again.');
	}
}

/**
 * Generates a paragraph with accompanying questions.
 * @param wire - The wire dependency injection container
 * @param params - Paragraph with questions generation parameters.
 * @returns A promise that resolves to a paragraph with questions result.
 * @throws {ValidationError} If parameters are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateParagraphWithQuestionsApi(
	wire: Wire,
	params: GenerateParagraphWithQuestionsParams
): Promise<ParagraphWithQuestionsResult> {
	// Validate input using Zod
	try {
		generateParagraphWithQuestionsSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const result = await llmService.generateParagraphWithQuestions(params);
		return result;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(`Failed to generate paragraph with questions:`, error);
		throw new Error('Failed to generate paragraph with questions. Please try again.');
	}
}

/**
 * Evaluates answers to questions and provides feedback.
 * @param wire - The wire dependency injection container
 * @param params - Answer evaluation parameters.
 * @returns A promise that resolves to an array of answer evaluation results.
 * @throws {ValidationError} If parameters are invalid.
 * @throws {Error} If evaluation fails for other reasons.
 */
export async function evaluateAnswersApi(
	wire: Wire,
	params: EvaluateAnswersParams
): Promise<AnswerEvaluationResult[]> {
	// Validate input using Zod
	try {
		evaluateAnswersSchema.parse(params);
	} catch (error) {
		if (error instanceof ZodError) handleValidationError(error);
		throw error;
	}

	const llmService = wire.llmService;
	try {
		const evaluations = await llmService.evaluateAnswers(params);
		return evaluations;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(`Failed to evaluate answers:`, error);
		throw new Error('Failed to evaluate answers. Please try again.');
	}
}

/**
 * Middleware for generating random words
 */
export async function generateRandomWordsMiddleware(ctx: AuthKoaContext): Promise<void> {
	const { keywordTerms, maxTerms, excludeCollectionIds, source_language, target_language } = (
		ctx.request as any
	).body;
	const words = await generateRandomWordsApi(
		ctx.wire,
		keywordTerms,
		maxTerms,
		excludeCollectionIds,
		source_language,
		target_language
	);
	ctx.body = words;
}

/**
 * Middleware for generating word details
 */
export async function generateWordDetailsMiddleware(ctx: AuthKoaContext): Promise<void> {
	const { terms, source_language, target_language } = (ctx.request as any).body;
	const words = await generateWordDetailsApi(ctx.wire, terms, source_language, target_language);
	ctx.body = words;
}

/**
 * Middleware for generating paragraphs
 */
export async function generateParagraphMiddleware(ctx: AuthKoaContext): Promise<void> {
	const { keywords, language, difficulty, count, sentenceCount } = (ctx.request as any).body;
	const paragraphs = await generateParagraphApi(
		ctx.wire,
		keywords,
		language,
		difficulty,
		count,
		sentenceCount
	);
	ctx.body = paragraphs;
}

/**
 * Middleware for grammar practice generation
 */
export async function generateGrammarPracticeMiddleware(ctx: AuthKoaContext): Promise<void> {
	const result = await generateGrammarPracticeApi(ctx.wire, (ctx.request as any).body);
	ctx.body = result;
}

/**
 * Middleware for translation evaluation
 */
export async function evaluateTranslationMiddleware(ctx: AuthKoaContext): Promise<void> {
	const result = await evaluateTranslationApi(ctx.wire, (ctx.request as any).body);
	ctx.body = result;
}

/**
 * Middleware for generating questions
 */
export async function generateQuestionsMiddleware(ctx: AuthKoaContext): Promise<void> {
	const questions = await generateQuestionsApi(ctx.wire, (ctx.request as any).body);
	ctx.body = questions;
}

/**
 * Middleware for generating paragraph with questions
 */
export async function generateParagraphWithQuestionsMiddleware(ctx: AuthKoaContext): Promise<void> {
	const result = await generateParagraphWithQuestionsApi(ctx.wire, (ctx.request as any).body);
	ctx.body = result;
}

/**
 * Middleware for evaluating answers
 */
export async function evaluateAnswersMiddleware(ctx: AuthKoaContext): Promise<void> {
	const result = await evaluateAnswersApi(ctx.wire, (ctx.request as any).body);
	ctx.body = result;
}
