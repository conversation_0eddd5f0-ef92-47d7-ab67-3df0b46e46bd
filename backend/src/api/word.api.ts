import { NotFoundError, UnauthorizedError, ValidationError } from '@/errors';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { AuthKoaContext } from '@/types/koa';
import { Wire } from '@/wire';

/**
 * Retrieves words in a specific collection for the authenticated user.
 * @param wire - The wire dependency injection container
 * @param collectionId - The ID of the collection.
 * @param userId - The authenticated user ID (provided by middleware).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {ValidationError} If collectionId is missing.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If fetching words fails for other reasons.
 */
export async function getWordsByCollectionApi(
	wire: Wire,
	collectionId: string,
	userId: string
): Promise<WordDetail[]> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required to fetch words.');
	}

	const wordService = wire.wordService;
	try {
		const words = await wordService.getWordsByCollection(userId, collectionId);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to fetch words for collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to fetch words by collection. Please try again.');
	}
}

/**
 * Saves a word as the "last seen" for the authenticated user.
 * @param wire - The wire dependency injection container
 * @param wordId - The ID of the word.
 * @param userId - The authenticated user ID (provided by middleware).
 * @param collectionId - Optional collection ID to track stats.
 * @returns A promise that resolves when the operation is complete.
 * @throws {ValidationError} If wordId is missing.
 * @throws {NotFoundError} If the specified word does not exist (if service throws this).
 * @throws {Error} If saving fails for other reasons.
 */
export async function createLastSeenWordApi(
	wire: Wire,
	wordId: string,
	userId: string,
	collectionId?: string
): Promise<void> {
	if (!wordId) {
		throw new ValidationError('Word ID is required to save as last seen.');
	}
	if (wordId.trim() === '') {
		throw new ValidationError('Word ID cannot be empty or whitespace.');
	}

	const lastSeenWordService = wire.lastSeenWordService;
	const collectionStatsService = wire.collectionStatsService;
	try {
		await lastSeenWordService.saveLastSeenWord(userId, wordId);

		// Track stats if collectionId is provided
		if (collectionId) {
			await collectionStatsService.trackWordReview(collectionId, userId, 1);
		}
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof UnauthorizedError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to create last seen word entry for word ${wordId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to save last seen word. Please try again.');
	}
}

/**
 * Retrieves words to review for the authenticated user from a specific collection.
 * @param wire - The wire dependency injection container
 * @param collectionId - The ID of the collection.
 * @param userId - The authenticated user ID (provided by middleware).
 * @param limit - Optional limit for the number of words (positive number).
 * @param offset - Optional offset for pagination (non-negative number).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {ValidationError} If collectionId is missing or limit/offset are invalid.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If fetching words fails for other reasons.
 */
export async function getWordsToReviewApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	limit?: number
): Promise<WordDetail[]> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required to get words for review.');
	}
	if (limit !== undefined && (typeof limit !== 'number' || limit < 1)) {
		throw new ValidationError('Limit must be a positive number if provided.');
	}

	const wordService = wire.wordService;
	try {
		// Assuming wordService.getWordsToReview can handle an undefined offset if not applicable by default
		const words = await wordService.getWordsToReview(userId, collectionId, limit);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to fetch words to review for collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to fetch words to review. Please try again.');
	}
}

/**
 * Searches for words within a specific collection based on a term and optional language.
 * Requires authentication.
 * @param wire - The wire dependency injection container
 * @param collectionId - The ID of the collection to search within.
 * @param userId - The authenticated user ID (provided by middleware).
 * @param term - The search term.
 * @param language - Optional language to filter by.
 * @param limit - Optional limit for the number of results (default 10, max 100).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {ValidationError} If collectionId or term is missing/empty, or limit is out of range, or language is invalid.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If search fails for other reasons.
 */
export async function searchWordsApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	term: string,
	language?: Language,
	limit: number = 10
): Promise<WordDetail[]> {
	if (!collectionId || typeof collectionId !== 'string' || collectionId.trim() === '') {
		throw new ValidationError('Collection ID is required and must be a non-empty string.');
	}

	term = term.trim();
	// It's okay for the term to be empty for this search, the service layer will handle it by returning all words in the collection (filtered by language if provided)
	// if (!term) {
	// 	throw new ValidationError('Search term is required.');
	// }

	if (typeof limit !== 'number' || limit < 1 || limit > 100) {
		throw new ValidationError('Limit must be a number between 1 and 100.');
	}
	if (language && !Object.values(Language).includes(language)) {
		throw new ValidationError(`Invalid language provided: ${language}`);
	}

	const wordService = wire.wordService;
	try {
		const words = await wordService.searchWordsInCollection(
			userId,
			collectionId,
			term,
			language,
			limit
		);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to search words in collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to search words. Please try again.');
	}
}

/**
 * Retrieves words by their IDs for the authenticated user.
 * @param wire - The wire dependency injection container
 * @param wordIds - Array of word IDs.
 * @param userId - The authenticated user ID (provided by middleware).
 * @returns A promise that resolves to an array of WordDetail objects.
 * @throws {ValidationError} If wordIds is missing or empty.
 * @throws {Error} If fetching words fails for other reasons.
 */
export async function getWordsByIdsApi(
	wire: Wire,
	wordIds: string[],
	userId: string
): Promise<WordDetail[]> {
	if (!wordIds || !Array.isArray(wordIds) || wordIds.length === 0) {
		throw new ValidationError('Word IDs array is required and must not be empty.');
	}

	const wordService = wire.wordService;
	try {
		const words = await wordService.findWordsByIds(wordIds);
		return words as WordDetail[]; // Assuming service returns WordDetail[] or compatible Word[]
	} catch (error) {
		if (
			error instanceof ValidationError ||
			error instanceof UnauthorizedError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(`Failed to fetch words by IDs for user ${userId}:`, error);
		throw new Error('Failed to fetch words by IDs. Please try again.');
	}
}

/**
 * Bulk deletes words from a collection for the authenticated user.
 * @param wire - The wire dependency injection container
 * @param collectionId - The ID of the collection.
 * @param userId - The authenticated user ID (provided by middleware).
 * @param wordIds - Array of word IDs to remove.
 * @returns A promise that resolves to the number of words removed.
 * @throws {ValidationError} If collectionId or wordIds is missing or empty.
 * @throws {NotFoundError} If the collection is not found for the user.
 * @throws {Error} If deletion fails for other reasons.
 */
export async function bulkDeleteWordsFromCollectionApi(
	wire: Wire,
	collectionId: string,
	userId: string,
	wordIds: string[]
): Promise<number> {
	if (!collectionId) {
		throw new ValidationError('Collection ID is required to delete words from collection.');
	}
	if (!wordIds || !Array.isArray(wordIds) || wordIds.length === 0) {
		throw new ValidationError('Word IDs array is required and must not be empty.');
	}

	const collectionService = wire.collectionService;
	try {
		const result = await collectionService.removeWordsFromCollection(
			userId,
			collectionId,
			wordIds
		);
		// The service returns the updated collection, so we need to calculate how many words were removed
		if (!result) {
			throw new NotFoundError('Collection', collectionId);
		}
		// For now, return the number of word IDs requested (assuming all were removed)
		// A more accurate implementation would compare before/after word counts
		return wordIds.length;
	} catch (error) {
		if (
			error instanceof UnauthorizedError ||
			error instanceof ValidationError ||
			error instanceof NotFoundError
		) {
			throw error;
		}
		console.error(
			`Failed to bulk delete words from collection ${collectionId}, user ${userId}:`,
			error
		);
		throw new Error('Failed to bulk delete words from collection. Please try again.');
	}
}

/**
 * Middleware to get words by collection ID
 */
export async function getWordsByCollectionMiddleware(ctx: AuthKoaContext): Promise<void> {
	const userId = ctx.state.user.id;
	const words = await getWordsByCollectionApi(ctx.wire, ctx.params.id, userId);
	ctx.body = words;
}

/**
 * Middleware to create last seen word
 */
export async function createLastSeenWordMiddleware(ctx: AuthKoaContext): Promise<void> {
	const userId = ctx.state.user.id;
	const { collectionId } = (ctx.request as any).body || {};
	await createLastSeenWordApi(ctx.wire, ctx.params.id, userId, collectionId);
	ctx.body = { success: true };
}

/**
 * Middleware to get words to review
 */
export async function getWordsToReviewMiddleware(ctx: AuthKoaContext): Promise<void> {
	const userId = ctx.state.user.id;
	const limit = ctx.query.limit ? parseInt(ctx.query.limit as string) : undefined;
	const words = await getWordsToReviewApi(ctx.wire, ctx.params.id, userId, limit);
	ctx.body = words;
}

/**
 * Middleware to search words in collection
 */
export async function searchWordsMiddleware(ctx: AuthKoaContext): Promise<void> {
	const userId = ctx.state.user.id;
	const { term, language, limit } = ctx.query;
	const words = await searchWordsApi(
		ctx.wire,
		ctx.params.id,
		userId,
		term as string,
		language as any,
		limit ? parseInt(limit as string) : undefined
	);
	ctx.body = words;
}

/**
 * Middleware to get words by IDs
 */
export async function getWordsByIdsMiddleware(ctx: AuthKoaContext): Promise<void> {
	const userId = ctx.state.user.id;
	const { wordIds } = (ctx.request as any).body;
	const words = await getWordsByIdsApi(ctx.wire, wordIds, userId);
	ctx.body = words;
}

/**
 * Middleware to bulk delete words from collection
 */
export async function bulkDeleteWordsFromCollectionMiddleware(ctx: AuthKoaContext): Promise<void> {
	const userId = ctx.state.user.id;
	const { wordIds } = (ctx.request as any).body;
	const deletedCount = await bulkDeleteWordsFromCollectionApi(
		ctx.wire,
		ctx.params.id,
		userId,
		wordIds
	);
	ctx.body = { deletedCount };
}
