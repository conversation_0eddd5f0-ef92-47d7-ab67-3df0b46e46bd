import { Context, Next } from 'koa';
import { UnauthorizedError, ValidationError } from '@/errors';
import { generateToken } from '@/utils/token.util';
import { Provider } from '@prisma/client';
import { Wire } from '@/wire';

/**
 * Logout middleware - clears the auth cookie
 */
export async function logoutMiddleware(ctx: Context, next: Next): Promise<void> {
	const authConfig = ctx.wire.config.authConfig;
	ctx.cookies.set(authConfig.jwtCookieName, '', {
		maxAge: 0,
		httpOnly: true,
		secure: false,
		path: '/',
	});

	ctx.status = 200;
	ctx.body = { success: true, message: 'Logged out successfully' };
}

/**
 * Provider login middleware - authenticates user with provider
 */
export async function providerLoginMiddleware(ctx: Context, next: Next): Promise<void> {
	const { provider, providerId } = (ctx.request as any).body as {
		provider: Provider;
		providerId: string;
	};

	if (!provider || !providerId || !Object.values(Provider).includes(provider)) {
		throw new ValidationError('Invalid provider or provider ID');
	}

	const authService = ctx.wire.authService;
	const user = await authService.providerLogin(provider, providerId);
	if (!user) throw new UnauthorizedError('Authentication failed');

	const token = await generateToken(ctx.wire, user);
	if (!token) throw new Error('Token generation failed');

	const authConfig = ctx.wire.config.authConfig;
	const serverConfig = ctx.wire.config.serverConfig;

	ctx.cookies.set(authConfig.jwtCookieName, token, {
		httpOnly: true,
		secure: serverConfig.env === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: authConfig.jwtExpiresIn * 1000, // Convert to milliseconds
	});

	ctx.status = 200;
	ctx.body = { success: true, user: { id: user.id, provider: user.provider } };
}

/**
 * Development login middleware - only available in development mode
 */
export async function developmentLoginMiddleware(ctx: Context, next: Next): Promise<void> {
	const serverConfig = ctx.wire.config.serverConfig;
	if (serverConfig.env !== 'development') {
		throw new Error('Development login is only available in development mode');
	}

	const authConfig = ctx.wire.config.authConfig;
	const defaultUser = authConfig.defaultUser;

	const authService = ctx.wire.authService;
	const user = await authService.providerLogin(defaultUser.provider, defaultUser.provider_id);
	if (!user) throw new UnauthorizedError('Authentication failed');

	const token = await generateToken(ctx.wire, user);
	if (!token) throw new Error('Token generation failed');

	ctx.cookies.set(authConfig.jwtCookieName, token, {
		httpOnly: true,
		secure: false,
		sameSite: 'strict',
		path: '/',
		maxAge: authConfig.jwtExpiresIn * 1000,
	});

	ctx.status = 200;
	ctx.body = { success: true, user: { id: user.id, provider: user.provider } };
}

/**
 * Username/password login middleware
 * Tự động tạo tài khoản mới nếu username chưa tồn tại
 */
export async function usernamePasswordLoginMiddleware(ctx: Context, next: Next): Promise<void> {
	const { username, password } = (ctx.request as any).body as {
		username: string;
		password: string;
	};

	if (!username || !password) {
		throw new ValidationError('Username and password are required');
	}

	if (username.trim().length < 3) {
		throw new ValidationError('Username must be at least 3 characters long');
	}

	if (password.length < 6) {
		throw new ValidationError('Password must be at least 6 characters long');
	}

	const authService = ctx.wire.authService;
	const user = await authService.usernamePasswordLogin(username.trim(), password);

	const token = await generateToken(ctx.wire, user);
	if (!token) throw new Error('Token generation failed');

	const authConfig = ctx.wire.config.authConfig;
	const serverConfig = ctx.wire.config.serverConfig;

	ctx.cookies.set(authConfig.jwtCookieName, token, {
		httpOnly: true,
		secure: serverConfig.env === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: authConfig.jwtExpiresIn * 1000,
	});

	ctx.status = 200;
	ctx.body = { success: true, user: { id: user.id, provider: user.provider } };
}

/**
 * Username/password login API function
 * Automatically creates a new account if username doesn't exist
 * @param wire - The wire dependency injection container
 * @param username - User's username (minimum 3 characters)
 * @param password - User's password (minimum 6 characters)
 * @throws {ValidationError} If username or password validation fails
 * @throws {UnauthorizedError} If authentication fails
 */
export async function usernamePasswordLoginApi(
	wire: Wire,
	username: string,
	password: string
): Promise<void> {
	if (!username || !password) {
		throw new ValidationError('Username and password are required');
	}

	if (username.trim().length < 3) {
		throw new ValidationError('Username must be at least 3 characters long');
	}

	if (password.length < 6) {
		throw new ValidationError('Password must be at least 6 characters long');
	}

	try {
		const authService = wire.authService;
		const user = await authService.usernamePasswordLogin(username.trim(), password);

		if (!user) {
			throw new UnauthorizedError('Authentication failed');
		}

		// Note: Cookie setting is handled by the Next.js middleware/route handler
		// This API function focuses on the authentication logic only
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		throw new Error(
			`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

/**
 * Logout API function
 * Clears user session and authentication
 */
export async function logoutApi(): Promise<void> {
	try {
		// Note: Cookie clearing is handled by the Next.js middleware/route handler
		// This API function can be extended with additional logout logic if needed
		// For now, this is a simple logout that relies on the frontend to clear cookies
		// Additional logout logic (like invalidating tokens, logging, etc.) can be added here
	} catch (error) {
		throw new Error(
			`Logout failed: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

/**
 * Logout API middleware for /api/auth/logout route
 * Clears the JWT cookie and returns success response
 */
export async function logoutApiMiddleware(ctx: Context, next: Next): Promise<void> {
	await logoutApi();
	const authConfig = ctx.wire.config.authConfig;
	ctx.cookies.set(authConfig.jwtCookieName, '', {
		maxAge: 0,
		httpOnly: true,
		secure: false,
		path: '/',
	});
	ctx.status = 200;
	ctx.body = { success: true, message: 'Logged out successfully' };
}

/**
 * Username/password login API middleware for /api/auth/username-password-login route
 * Authenticates user and sets JWT cookie
 */
export async function usernamePasswordLoginApiMiddleware(ctx: Context, next: Next): Promise<void> {
	const { username, password } = (ctx.request as any).body as {
		username: string;
		password: string;
	};

	await usernamePasswordLoginApi(ctx.wire, username, password);

	// Handle token generation and cookie setting
	const authService = ctx.wire.authService;
	const user = await authService.usernamePasswordLogin(username.trim(), password);
	const token = await generateToken(ctx.wire, user);
	if (!token) throw new Error('Token generation failed');

	const authConfig = ctx.wire.config.authConfig;
	const serverConfig = ctx.wire.config.serverConfig;

	ctx.cookies.set(authConfig.jwtCookieName, token, {
		httpOnly: true,
		secure: serverConfig.env === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: authConfig.jwtExpiresIn * 1000,
	});

	ctx.status = 200;
	ctx.body = { success: true, user: { id: user.id, provider: user.provider } };
}
