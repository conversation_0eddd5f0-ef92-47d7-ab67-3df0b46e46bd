import { ValidationError } from '@/errors';
import { AuthKoaContext } from '@/types/koa';
import { Next } from 'koa';

/**
 * Save last seen word middleware
 * POST /last-seen-words
 */
export async function saveLastSeenWordMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const data = (ctx.request as any).body as { wordId: string };

	if (!data.wordId || data.wordId.trim().length === 0) {
		throw new ValidationError('Word ID is required');
	}

	const lastSeenWordService = ctx.wire.lastSeenWordService;
	await lastSeenWordService.saveLastSeenWord(userId, data.wordId.trim());

	ctx.status = 200;
	ctx.body = { success: true };
}
