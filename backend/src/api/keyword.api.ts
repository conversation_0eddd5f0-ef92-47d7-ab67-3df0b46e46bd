import { NotFoundError, ValidationError } from '@/errors';
import { AuthKoaContext } from '@/types/koa';
import { Next } from 'koa';

/**
 * Get user keywords middleware
 * GET /keywords
 */
export async function getUserKeywordsMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const keywordService = ctx.wire.keywordService;
	const keywords = await keywordService.getUserKeywords(userId);

	ctx.status = 200;
	ctx.body = keywords;
}

/**
 * Create keyword middleware
 * POST /keywords
 */
export async function createKeywordMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const data = (ctx.request as any).body as { content: string };

	if (!data.content || data.content.trim().length === 0) {
		throw new ValidationError('Keyword content is required');
	}

	if (data.content.length > 255) {
		throw new ValidationError('Keyword content must be less than 255 characters');
	}

	const keywordService = ctx.wire.keywordService;
	const keyword = await keywordService.createKeyword(userId, data.content.trim());

	ctx.status = 201;
	ctx.body = keyword;
}

/**
 * Update keyword middleware
 * PUT /keywords/:id
 */
export async function updateKeywordMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const keywordId = ctx.params.id;
	const data = (ctx.request as any).body as { content: string };

	if (!keywordId) {
		throw new ValidationError('Keyword ID is required');
	}

	if (!data.content || data.content.trim().length === 0) {
		throw new ValidationError('Keyword content is required');
	}

	if (data.content.length > 255) {
		throw new ValidationError('Keyword content must be less than 255 characters');
	}

	const keywordService = ctx.wire.keywordService;
	const keyword = await keywordService.updateKeyword(keywordId, data.content.trim());

	if (!keyword) {
		throw new NotFoundError('Keyword', keywordId);
	}

	ctx.status = 200;
	ctx.body = keyword;
}

/**
 * Delete keyword middleware
 * DELETE /keywords/:id
 */
export async function deleteKeywordMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const keywordId = ctx.params.id;

	if (!keywordId) {
		throw new ValidationError('Keyword ID is required');
	}

	const keywordService = ctx.wire.keywordService;
	const success = await keywordService.deleteKeyword(keywordId);

	if (!success) {
		throw new NotFoundError('Keyword', keywordId);
	}

	ctx.status = 200;
	ctx.body = { success: true };
}

/**
 * Get keyword by ID middleware
 * GET /keywords/:id
 */
export async function getKeywordMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const keywordId = ctx.params.id;

	if (!keywordId) {
		throw new ValidationError('Keyword ID is required');
	}

	const keywordService = ctx.wire.keywordService;
	const keyword = await keywordService.getKeywordById(keywordId);

	if (!keyword) {
		throw new NotFoundError('Keyword', keywordId);
	}

	ctx.status = 200;
	ctx.body = keyword;
}
