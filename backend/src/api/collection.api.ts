import { NotFoundError, ValidationError } from '@/errors';
import { Language } from '@prisma/client';
import { Context, Next } from 'koa';
import { AuthKoaContext } from '@/types/koa';

/**
 * Delete collection middleware
 * DELETE /collections/:id
 */
export async function deleteCollectionMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}

	const collectionService = ctx.wire.collectionService;
	const result = await collectionService.deleteCollection(userId, collectionId);

	ctx.status = 200;
	ctx.body = { success: result };
}

/**
 * Update collection middleware
 * PUT /collections/:id
 */
export async function updateCollectionMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const data = (ctx.request as any).body as {
		name?: string;
		wordIds?: string[];
	};

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!data || typeof data !== 'object') {
		throw new ValidationError('Update data object is required');
	}

	const { name, wordIds } = data;
	const collectionService = ctx.wire.collectionService;

	const updatedCollection = await collectionService.updateCollection(
		userId,
		collectionId,
		name,
		wordIds
	);

	if (!updatedCollection) {
		throw new NotFoundError('Collection', collectionId);
	}

	ctx.status = 200;
	ctx.body = updatedCollection;
}

/**
 * Get collection middleware
 * GET /collections/:id
 */
export async function getCollectionMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}

	const collectionService = ctx.wire.collectionService;
	const collection = await collectionService.getCollectionById(userId, collectionId);

	if (!collection) {
		throw new NotFoundError('Collection', collectionId);
	}

	ctx.status = 200;
	ctx.body = collection;
}

/**
 * Get all collections middleware
 * GET /collections
 */
export async function getCollectionsMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionService = ctx.wire.collectionService;
	const collections = await collectionService.getUserCollections(userId);

	ctx.status = 200;
	ctx.body = collections;
}

/**
 * Create collection middleware
 * POST /collections
 */
export async function createCollectionMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const data = (ctx.request as any).body as {
		name: string;
		target_language: Language;
		source_language: Language;
		wordIds?: string[];
	};

	if (!data.name) {
		throw new ValidationError('Collection name is required');
	}
	if (!data.target_language || !Object.values(Language).includes(data.target_language)) {
		throw new ValidationError(`Invalid target_language provided: ${data.target_language}`);
	}
	if (!data.source_language || !Object.values(Language).includes(data.source_language)) {
		throw new ValidationError(`Invalid source_language provided: ${data.source_language}`);
	}

	const collectionService = ctx.wire.collectionService;
	const collection = await collectionService.createCollection(
		userId,
		data.name,
		data.target_language,
		data.source_language,
		data.wordIds
	);

	ctx.status = 201;
	ctx.body = collection;
}

/**
 * Add word to collection middleware
 * POST /collections/:id/words
 */
export async function addWordToCollectionMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const data = (ctx.request as any).body as { wordId: string };

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!data.wordId) {
		throw new ValidationError('Word ID is required');
	}

	const collectionService = ctx.wire.collectionService;
	const updatedCollection = await collectionService.addWordsToCollection(userId, collectionId, [
		data.wordId,
	]);

	ctx.status = 200;
	ctx.body = updatedCollection;
}

/**
 * Add term to collection middleware
 * POST /collections/:id/terms
 */
export async function addTermToCollectionMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const data = (ctx.request as any).body as { term: string; language: Language };

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!data.term) {
		throw new ValidationError('Term is required');
	}
	if (!data.language || !Object.values(Language).includes(data.language)) {
		throw new ValidationError('Valid language is required');
	}

	const collectionService = ctx.wire.collectionService;
	const updatedCollection = await collectionService.addTermsToCollection(
		userId,
		collectionId,
		[data.term],
		data.language
	);

	ctx.status = 200;
	ctx.body = updatedCollection;
}

/**
 * Remove words from collection middleware
 * DELETE /collections/:id/words
 */
export async function removeWordsFromCollectionMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const data = (ctx.request as any).body as { wordIds: string[] };

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!data.wordIds || !Array.isArray(data.wordIds) || data.wordIds.length === 0) {
		throw new ValidationError('Valid word IDs array is required');
	}

	const collectionService = ctx.wire.collectionService;
	const updatedCollection = await collectionService.removeWordsFromCollection(
		userId,
		collectionId,
		data.wordIds
	);

	ctx.status = 200;
	ctx.body = updatedCollection;
}

/**
 * Add multiple words to collection middleware
 * PUT /collections/:id/words
 */
export async function addWordsToCollectionMiddleware(
	ctx: AuthKoaContext,
	next: Next
): Promise<void> {
	const userId = ctx.state.user.id;
	const collectionId = ctx.params.id;
	const data = (ctx.request as any).body as { wordIds: string[] };

	if (!collectionId) {
		throw new ValidationError('Collection ID is required');
	}
	if (!data.wordIds || !Array.isArray(data.wordIds) || data.wordIds.length === 0) {
		throw new ValidationError('Valid word IDs array is required');
	}

	const collectionService = ctx.wire.collectionService;
	const updatedCollection = await collectionService.addWordsToCollection(
		userId,
		collectionId,
		data.wordIds
	);

	ctx.status = 200;
	ctx.body = updatedCollection;
}
