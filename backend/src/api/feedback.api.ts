import { ValidationError } from '@/errors';
import { AuthKoaContext } from '@/types/koa';
import { Next } from 'koa';

/**
 * Submit feedback middleware
 * POST /feedback
 */
export async function submitFeedbackMiddleware(ctx: AuthKoaContext, next: Next): Promise<void> {
	const userId = ctx.state.user.id;
	const data = (ctx.request as any).body as { message: string };

	if (!data.message || data.message.trim().length === 0) {
		throw new ValidationError('Feedback message is required');
	}

	if (data.message.length > 1000) {
		throw new ValidationError('Feedback message must be less than 1000 characters');
	}

	const feedbackService = ctx.wire.feedbackService;
	const feedback = await feedbackService.createFeedback(data.message.trim(), userId);

	ctx.status = 201;
	ctx.body = { success: true, id: feedback.id };
}
