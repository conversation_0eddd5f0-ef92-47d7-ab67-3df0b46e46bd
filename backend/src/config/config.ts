import { Provider } from '@prisma/client';

const env = process.env;

interface ServerConfig {
	readonly port: number;
	readonly env: string;
}

interface AuthConfig {
	readonly jwtCookieName: string;
	readonly jwtSecret: string;
	readonly jwtExpiresIn: number;
	readonly defaultUser: {
		readonly provider: Provider;
		readonly provider_id: string;
	};
}

interface LLMConfig {
	readonly openAIKey: string;
	readonly openAIModel: string;
	readonly maxExamples: number;
	readonly temperature: number;
	readonly maxTokens: number;
}

export class Config {
	readonly serverConfig: ServerConfig;
	readonly authConfig: AuthConfig;
	readonly llmConfig: LLMConfig;

	constructor() {
		// Initialize all configurations in constructor
		this.serverConfig = {
			port: Number.parseInt(env.PORT || '5000', 10),
			env: env.NODE_ENV || 'development',
		};

		this.authConfig = {
			jwtCookieName: env.JWT_COOKIE_NAME || 'auth_token',
			jwtSecret: env.JWT_SECRET || 'your-secret-key',
			jwtExpiresIn: Number(env.JWT_EXPIRES_IN) || 30 * 24 * 60 * 60,
			// Default user for development environment
			defaultUser: {
				provider: Provider.TELEGRAM,
				provider_id: '711352287',
			},
		};

		this.llmConfig = {
			openAIKey: env.LLM_OPENAI_API_KEY || '',
			openAIModel: env.LLM_OPENAI_MODEL || 'gpt-4o-mini',
			maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || '8'),
			temperature: Number.parseFloat(env.LLM_TEMPERATURE || '0.7'),
			maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || '1000'),
		};
	}
}
