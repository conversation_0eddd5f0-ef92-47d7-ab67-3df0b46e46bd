import { CollectionWithDetail } from '@/models';
import { Collection, Language, Prisma, Word } from '@prisma/client';
import { Wire } from '@/wire';

export interface CollectionService {
	getUserCollections(userId: string): Promise<CollectionWithDetail[]>;
	createCollection(
		userId: string,
		name: string,
		target_language: Language,
		source_language: Language,
		wordIds?: string[]
	): Promise<CollectionWithDetail>;
	getCollectionById(userId: string, collectionId: string): Promise<CollectionWithDetail | null>;
	updateCollection(
		userId: string,
		collectionId: string,
		name?: string,
		wordIds?: string[]
	): Promise<CollectionWithDetail | null>;
	deleteCollection(userId: string, collectionId: string): Promise<boolean>;
	addWordsToCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null>;
	addTermsToCollection(
		userId: string,
		collectionId: string,
		terms: string[],
		language: Language
	): Promise<CollectionWithDetail | null>;
	removeWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null>;
}

export class CollectionServiceImpl implements CollectionService {
	constructor(private readonly wire: Wire) {}

	private async _enrichCollection(
		// Collection type here now includes target_language and source_language
		collection: (Collection & { words?: Word[] | null }) | null
	): Promise<CollectionWithDetail | null> {
		if (!collection) return null;
		const words = collection.word_ids.length
			? await this.wire.wordService.findWordsByIds(collection.word_ids)
			: [];
		return {
			...collection, // Spreading collection includes the new language fields
			words,
		} as CollectionWithDetail; // Cast, assuming CollectionWithDetail interface is updated
	}

	private async _enrichCollections(
		// Collection type here now includes target_language and source_language
		collections: (Collection & { words?: Word[] | null })[]
	): Promise<CollectionWithDetail[]> {
		if (!collections || collections.length === 0) {
			return [];
		}
		const enriched = await Promise.all(collections.map((c) => this._enrichCollection(c)));
		return enriched.filter((c): c is CollectionWithDetail => c !== null);
	}

	async getUserCollections(userId: string): Promise<CollectionWithDetail[]> {
		const repoCollections = await this.wire.collectionRepository.findUserCollections(userId);
		repoCollections.sort(
			(a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
		);
		const enrichedCollections = await this._enrichCollections(repoCollections);
		return enrichedCollections;
	}

	async createCollection(
		userId: string,
		name: string,
		target_language: Language, // Accept new field
		source_language: Language, // Accept new field
		wordIds?: string[]
	): Promise<CollectionWithDetail> {
		const createInput: Prisma.CollectionCreateInput = {
			name,
			target_language, // Pass new field
			source_language, // Pass new field
			user: { connect: { id: userId } },
			...(wordIds && wordIds.length > 0 ? { word_ids: wordIds } : {}),
		};
		const repoCollection = await this.wire.collectionRepository.create(createInput);
		const enrichedCollection = await this._enrichCollection(repoCollection);
		if (!enrichedCollection) {
			throw new Error('Failed to create collection');
		}
		return enrichedCollection;
	}

	async getCollectionById(
		userId: string,
		collectionId: string
	): Promise<CollectionWithDetail | null> {
		const repoCollection = await this.wire.collectionRepository.findOne({
			id: collectionId,
			user_id: userId,
		});
		const enrichedCollection = await this._enrichCollection(repoCollection);
		return enrichedCollection;
	}

	async updateCollection(
		userId: string,
		collectionId: string,
		name?: string,
		wordIds?: string[]
	): Promise<CollectionWithDetail | null> {
		const existingCollection = await this.wire.collectionRepository.findOne({
			id: collectionId,
			user_id: userId,
		});

		if (!existingCollection) {
			return null;
		}

		const updateData: Prisma.CollectionUpdateInput = {};
		if (name !== undefined) {
			// Check explicitly for undefined to allow empty string name
			updateData.name = name;
		}
		if (wordIds !== undefined) {
			// Check explicitly for undefined
			updateData.word_ids = wordIds;
		}

		if (Object.keys(updateData).length === 0) {
			const enriched = await this._enrichCollection(existingCollection);
			return enriched;
		}

		try {
			const updatedRepoCollection = await this.wire.collectionRepository.update(
				collectionId,
				updateData
			);
			const enrichedCollection = await this._enrichCollection(updatedRepoCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}

	async deleteCollection(userId: string, collectionId: string): Promise<boolean> {
		const collection = await this.wire.collectionRepository.findOne({
			id: collectionId,
			user_id: userId,
		});

		if (!collection) {
			return false;
		}
		await this.wire.collectionRepository.delete({ id: collectionId });
		return true;
	}
	async addWordsToCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null> {
		const collectionExists = await this.wire.collectionRepository.findOne({
			id: collectionId,
			user_id: userId,
		});
		if (!collectionExists) {
			return null;
		}

		try {
			const currentWordIds: string[] = Array.isArray(collectionExists.word_ids)
				? collectionExists.word_ids
				: [];

			const mergedWordIds = Array.from(new Set([...currentWordIds, ...wordIds]));

			const updatedCollection = await this.wire.collectionRepository.update(collectionId, {
				word_ids: mergedWordIds,
			});

			const enrichedCollection = await this._enrichCollection(updatedCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}

	async addTermsToCollection(
		userId: string,
		collectionId: string,
		terms: string[],
		language: Language
	): Promise<CollectionWithDetail | null> {
		if (!terms || terms.length === 0) {
			// Return the collection without modification if no terms to add
			const collectionExists = await this.wire.collectionRepository.findOne({
				id: collectionId,
				user_id: userId,
			});
			if (!collectionExists) {
				return null;
			}
			return this._enrichCollection(collectionExists);
		}

		try {
			// LLM generation and database operations
			const generatedWords = await this.wire.llmService.generateWordDetails(
				terms,
				language,
				language // For source_language, we use the same language as target
			);
			const wordIds = generatedWords.map((w) => w.id);

			const updatedCollection = await this.addWordsToCollection(
				userId,
				collectionId,
				wordIds
			);
			return updatedCollection;
		} catch (error) {
			console.error('Error adding terms to collection:', error);
			throw new Error(
				`Failed to add terms to collection: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`
			);
		}
	}

	async removeWordsFromCollection(
		userId: string,
		collectionId: string,
		wordIds: string[]
	): Promise<CollectionWithDetail | null> {
		const collectionExists = await this.wire.collectionRepository.findOne({
			id: collectionId,
			user_id: userId,
		});
		if (!collectionExists) {
			return null;
		}

		try {
			const currentWordIds: string[] = Array.isArray(collectionExists.word_ids)
				? collectionExists.word_ids
				: [];

			const updatedWordIds = currentWordIds.filter((wordId) => !wordIds.includes(wordId));

			const updatedCollection = await this.wire.collectionRepository.update(collectionId, {
				word_ids: updatedWordIds,
			});

			const enrichedCollection = await this._enrichCollection(updatedCollection);
			return enrichedCollection;
		} catch (error: any) {
			if (error && typeof error.code === 'string' && error.code === 'P2025') {
				return null;
			}
			throw error;
		}
	}
}
