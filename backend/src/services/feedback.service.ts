import { Feedback } from '@prisma/client';
import { Wire } from '@/wire';

export interface FeedbackService {
	createFeedback(message: string, userId: string): Promise<Feedback>;
}

export class FeedbackServiceImpl implements FeedbackService {
	constructor(private readonly wire: Wire) {}

	async createFeedback(message: string, userId: string): Promise<Feedback> {
		const createdFeedback = await this.wire.feedbackRepository.create({
			message,
			user: {
				connect: { id: userId },
			},
		});

		const feedback = await this.wire.feedbackRepository.findById(createdFeedback.id);
		if (!feedback) {
			throw new Error(
				`Failed to retrieve created feedback with ID ${createdFeedback.id} including user relation.`
			);
		}

		return feedback;
	}
}
