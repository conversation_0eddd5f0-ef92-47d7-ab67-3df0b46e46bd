import { CollectionStats } from '@prisma/client';
import { Wire } from '@/wire';

export interface DailyStats {
	date: string; // YYYY-MM-DD format
	words_reviewed_count: number;
	qa_practice_submissions: number;
	paragraph_practice_submissions: number;
}

export interface CollectionStatsService {
	getStatsForCollection(
		collectionId: string,
		userId: string,
		days?: number
	): Promise<DailyStats[]>;

	trackWordReview(collectionId: string, userId: string, count?: number): Promise<void>;

	trackQAPracticeSubmission(collectionId: string, userId: string, count?: number): Promise<void>;

	trackParagraphPracticeSubmission(
		collectionId: string,
		userId: string,
		count?: number
	): Promise<void>;

	getTodayStats(collectionId: string, userId: string): Promise<DailyStats>;
}

export class CollectionStatsServiceImpl implements CollectionStatsService {
	constructor(private readonly wire: Wire) {}

	async getStatsForCollection(
		collectionId: string,
		userId: string,
		days: number = 30
	): Promise<DailyStats[]> {
		const endDate = new Date();
		const startDate = new Date();
		startDate.setDate(endDate.getDate() - days + 1);

		// Normalize dates to start of day
		startDate.setHours(0, 0, 0, 0);
		endDate.setHours(23, 59, 59, 999);

		const stats = await this.wire.collectionStatsRepository.getStatsByCollectionAndDateRange(
			collectionId,
			userId,
			startDate,
			endDate
		);

		// Create a map for quick lookup
		const statsMap = new Map<string, CollectionStats>();
		stats.forEach((stat) => {
			const dateKey = this.formatDate(stat.date);
			statsMap.set(dateKey, stat);
		});

		// Generate array for all days in range, filling missing days with zeros
		const result: DailyStats[] = [];
		for (let i = 0; i < days; i++) {
			const currentDate = new Date(startDate);
			currentDate.setDate(startDate.getDate() + i);
			const dateKey = this.formatDate(currentDate);

			const stat = statsMap.get(dateKey);
			result.push({
				date: dateKey,
				words_reviewed_count: stat?.words_reviewed_count || 0,
				qa_practice_submissions: stat?.qa_practice_submissions || 0,
				paragraph_practice_submissions: stat?.paragraph_practice_submissions || 0,
			});
		}

		return result;
	}

	async trackWordReview(collectionId: string, userId: string, count: number = 1): Promise<void> {
		const today = new Date();
		await this.wire.collectionStatsRepository.incrementWordsReviewed(
			collectionId,
			userId,
			today,
			count
		);
	}

	async trackQAPracticeSubmission(
		collectionId: string,
		userId: string,
		count: number = 1
	): Promise<void> {
		const today = new Date();
		await this.wire.collectionStatsRepository.incrementQAPracticeSubmissions(
			collectionId,
			userId,
			today,
			count
		);
	}

	async trackParagraphPracticeSubmission(
		collectionId: string,
		userId: string,
		count: number = 1
	): Promise<void> {
		const today = new Date();
		await this.wire.collectionStatsRepository.incrementParagraphPracticeSubmissions(
			collectionId,
			userId,
			today,
			count
		);
	}

	async getTodayStats(collectionId: string, userId: string): Promise<DailyStats> {
		const today = new Date();
		const stat = await this.wire.collectionStatsRepository.getStatsByCollectionAndDate(
			collectionId,
			userId,
			today
		);

		return {
			date: this.formatDate(today),
			words_reviewed_count: stat?.words_reviewed_count || 0,
			qa_practice_submissions: stat?.qa_practice_submissions || 0,
			paragraph_practice_submissions: stat?.paragraph_practice_submissions || 0,
		};
	}

	private formatDate(date: Date): string {
		return date.toISOString().split('T')[0]; // YYYY-MM-DD format
	}
}
