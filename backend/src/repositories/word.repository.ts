import type { Language, Prisma, PrismaClient, Word } from '@prisma/client';
import { type BaseRepository, BaseRepositoryImpl } from './base.repository';

// Define the include structure for Word queries
export const wordInclude = {
	definitions: {
		include: {
			explains: true,
			examples: true,
		},
	},
};

export interface WordRepository extends BaseRepository<Word> {
	find(query: Record<string, unknown>, limit?: number): Promise<Word[]>;
	searchWords(term: string, language?: Language, limit?: number): Promise<Word[]>;
	findOrCreateWords(terms: string[], language: Language): Promise<Word[]>;
	findWordsByIds(wordIds: string[]): Promise<Word[]>;
	findByTerm(term: string, language: Language): Promise<Word | null>;
}

export class WordRepositoryImpl extends BaseRepositoryImpl<Word> implements WordRepository {
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.word);
	}

	override async findById(id: string): Promise<Word | null> {
		const word = await this.prisma.word.findUnique({
			where: { id },
			include: wordInclude,
		});
		return word;
	}

	override async findOne(query: Record<string, unknown>): Promise<Word | null> {
		const word = await this.prisma.word.findFirst({
			where: query,
			include: wordInclude,
		});
		return word;
	}

	override async find(query: Prisma.WordWhereInput, limit?: number): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: query,
			include: wordInclude,
			take: limit,
		});
		return words;
	}

	override async create(data: Prisma.WordCreateInput): Promise<Word> {
		if (!data.term || !data.language) {
			throw new Error('Term and language are required');
		}

		const word = await this.prisma.word.create({
			data,
			include: wordInclude,
		});
		return word;
	}

	override async update(id: string, data: Prisma.WordUpdateInput): Promise<Word> {
		const word = await this.prisma.word.update({
			where: { id },
			data,
			include: wordInclude,
		});
		return word;
	}

	override async delete(query: Record<string, unknown>): Promise<void> {
		await this.prisma.word.deleteMany({
			where: query,
		});
	}

	async searchWords(term: string, language?: Language, limit = 10): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				term: {
					contains: term,
					mode: 'insensitive',
				},
				...(language && { language }),
			},
			include: wordInclude,
			take: limit,
		});
		return words;
	}

	async findOrCreateWords(terms: string[], language: Language): Promise<Word[]> {
		const existingWords = await this.prisma.word.findMany({
			where: {
				term: {
					in: terms,
				},
				language,
			},
			include: wordInclude,
		});

		const existingTerms = new Set(existingWords.map((w) => w.term));
		const newTerms = terms.filter((term) => !existingTerms.has(term));

		if (newTerms.length === 0) {
			return existingWords;
		}

		const newWords = await Promise.all(
			newTerms.map((term) =>
				this.prisma.word.create({
					data: {
						term,
						language,
					},
					include: wordInclude,
				})
			)
		);

		return [...existingWords, ...newWords];
	}

	async findWordsByIds(wordIds: string[]): Promise<Word[]> {
		const words = await this.prisma.word.findMany({
			where: {
				id: {
					in: wordIds,
				},
			},
			include: wordInclude,
		});
		return words;
	}

	async findByTerm(term: string, language: Language): Promise<Word | null> {
		const word = await this.prisma.word.findFirst({
			where: {
				term,
				language,
			},
			include: wordInclude,
		});
		return word;
	}
}
