/*
  Warnings:

  - You are about to drop the column `created_at` on the `Definition` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Definition` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `Example` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Example` table. All the data in the column will be lost.
  - You are about to drop the column `paragraphId` on the `Exercise` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `Explain` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `Explain` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `Feedback` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `Feedback` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `Feedback` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `Feedback` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `Keyword` table. All the data in the column will be lost.
  - You are about to drop the column `language` on the `Keyword` table. All the data in the column will be lost.
  - You are about to drop the column `meaning` on the `Keyword` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `Keyword` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `Keyword` table. All the data in the column will be lost.
  - You are about to drop the column `word` on the `Keyword` table. All the data in the column will be lost.
  - You are about to drop the column `category_id` on the `LastSeenWord` table. All the data in the column will be lost.
  - You are about to drop the column `collection_id` on the `LastSeenWord` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `LastSeenWord` table. All the data in the column will be lost.
  - You are about to drop the column `priority_score` on the `LastSeenWord` table. All the data in the column will be lost.
  - You are about to drop the column `retention_score` on the `LastSeenWord` table. All the data in the column will be lost.
  - You are about to drop the column `updated_at` on the `LastSeenWord` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `Paragraph` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `Paragraph` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `Paragraph` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `UserSettings` table. All the data in the column will be lost.
  - You are about to drop the column `defaultLength` on the `UserSettings` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `UserSettings` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `UserSettings` table. All the data in the column will be lost.
  - You are about to drop the `Category` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `ParagraphStats` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Test` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TestDetail` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `TestResult` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_KeywordToParagraph` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_WordToCollection` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[user_id]` on the table `UserSettings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `paragraph_id` to the `Exercise` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `Feedback` table without a default value. This is not possible if the table is not empty.
  - Added the required column `content` to the `Keyword` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `Keyword` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `UserSettings` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Category" DROP CONSTRAINT "Category_user_id_fkey";

-- DropForeignKey
ALTER TABLE "Exercise" DROP CONSTRAINT "Exercise_paragraphId_fkey";

-- DropForeignKey
ALTER TABLE "Feedback" DROP CONSTRAINT "Feedback_userId_fkey";

-- DropForeignKey
ALTER TABLE "Keyword" DROP CONSTRAINT "Keyword_userId_fkey";

-- DropForeignKey
ALTER TABLE "LastSeenWord" DROP CONSTRAINT "LastSeenWord_category_id_fkey";

-- DropForeignKey
ALTER TABLE "LastSeenWord" DROP CONSTRAINT "LastSeenWord_collection_id_fkey";

-- DropForeignKey
ALTER TABLE "LastSeenWord" DROP CONSTRAINT "LastSeenWord_word_id_fkey";

-- DropForeignKey
ALTER TABLE "Paragraph" DROP CONSTRAINT "Paragraph_userId_fkey";

-- DropForeignKey
ALTER TABLE "ParagraphStats" DROP CONSTRAINT "ParagraphStats_paragraphId_fkey";

-- DropForeignKey
ALTER TABLE "Test" DROP CONSTRAINT "Test_user_id_fkey";

-- DropForeignKey
ALTER TABLE "TestDetail" DROP CONSTRAINT "TestDetail_test_result_id_fkey";

-- DropForeignKey
ALTER TABLE "TestResult" DROP CONSTRAINT "TestResult_test_id_fkey";

-- DropForeignKey
ALTER TABLE "UserSettings" DROP CONSTRAINT "UserSettings_userId_fkey";

-- DropForeignKey
ALTER TABLE "_KeywordToParagraph" DROP CONSTRAINT "_KeywordToParagraph_A_fkey";

-- DropForeignKey
ALTER TABLE "_KeywordToParagraph" DROP CONSTRAINT "_KeywordToParagraph_B_fkey";

-- DropForeignKey
ALTER TABLE "_WordToCollection" DROP CONSTRAINT "_WordToCollection_A_fkey";

-- DropForeignKey
ALTER TABLE "_WordToCollection" DROP CONSTRAINT "_WordToCollection_B_fkey";

-- DropIndex
DROP INDEX "Exercise_paragraphId_idx";

-- DropIndex
DROP INDEX "Feedback_email_idx";

-- DropIndex
DROP INDEX "Feedback_status_idx";

-- DropIndex
DROP INDEX "Feedback_userId_idx";

-- DropIndex
DROP INDEX "Keyword_userId_idx";

-- DropIndex
DROP INDEX "Keyword_word_idx";

-- DropIndex
DROP INDEX "Keyword_word_userId_key";

-- DropIndex
DROP INDEX "LastSeenWord_user_id_word_id_key";

-- DropIndex
DROP INDEX "Paragraph_userId_idx";

-- DropIndex
DROP INDEX "UserSettings_userId_key";

-- AlterTable
ALTER TABLE "Collection" ADD COLUMN     "keyword_ids" TEXT[],
ADD COLUMN     "paragraph_ids" TEXT[];

-- AlterTable
ALTER TABLE "Definition" DROP COLUMN "created_at",
DROP COLUMN "updated_at",
ALTER COLUMN "ipa" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Example" DROP COLUMN "created_at",
DROP COLUMN "updated_at";

-- AlterTable
ALTER TABLE "Exercise" DROP COLUMN "paragraphId",
ADD COLUMN     "paragraph_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Explain" DROP COLUMN "created_at",
DROP COLUMN "updated_at";

-- AlterTable
ALTER TABLE "Feedback" DROP COLUMN "createdAt",
DROP COLUMN "email",
DROP COLUMN "status",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Keyword" DROP COLUMN "createdAt",
DROP COLUMN "language",
DROP COLUMN "meaning",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
DROP COLUMN "word",
ADD COLUMN     "content" TEXT NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "LastSeenWord" DROP COLUMN "category_id",
DROP COLUMN "collection_id",
DROP COLUMN "created_at",
DROP COLUMN "priority_score",
DROP COLUMN "retention_score",
DROP COLUMN "updated_at";

-- AlterTable
ALTER TABLE "Paragraph" DROP COLUMN "createdAt",
DROP COLUMN "updatedAt",
DROP COLUMN "userId";

-- AlterTable
ALTER TABLE "UserSettings" DROP COLUMN "createdAt",
DROP COLUMN "defaultLength",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
ADD COLUMN     "user_id" TEXT NOT NULL;

-- DropTable
DROP TABLE "Category";

-- DropTable
DROP TABLE "ParagraphStats";

-- DropTable
DROP TABLE "Test";

-- DropTable
DROP TABLE "TestDetail";

-- DropTable
DROP TABLE "TestResult";

-- DropTable
DROP TABLE "_KeywordToParagraph";

-- DropTable
DROP TABLE "_WordToCollection";

-- CreateIndex
CREATE INDEX "Collection_user_id_idx" ON "Collection"("user_id");

-- CreateIndex
CREATE INDEX "Exercise_paragraph_id_idx" ON "Exercise"("paragraph_id");

-- CreateIndex
CREATE INDEX "Feedback_user_id_idx" ON "Feedback"("user_id");

-- CreateIndex
CREATE INDEX "Keyword_user_id_idx" ON "Keyword"("user_id");

-- CreateIndex
CREATE INDEX "LastSeenWord_user_id_idx" ON "LastSeenWord"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "UserSettings_user_id_key" ON "UserSettings"("user_id");

-- AddForeignKey
ALTER TABLE "Keyword" ADD CONSTRAINT "Keyword_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Exercise" ADD CONSTRAINT "Exercise_paragraph_id_fkey" FOREIGN KEY ("paragraph_id") REFERENCES "Paragraph"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserSettings" ADD CONSTRAINT "UserSettings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
