-- CreateTable
CREATE TABLE "CollectionStats" (
    "id" TEXT NOT NULL,
    "collection_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "date" DATE NOT NULL,
    "words_reviewed_count" INTEGER NOT NULL DEFAULT 0,
    "qa_practice_submissions" INTEGER NOT NULL DEFAULT 0,
    "paragraph_practice_submissions" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CollectionStats_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CollectionStats_collection_id_idx" ON "CollectionStats"("collection_id");

-- CreateIndex
CREATE INDEX "CollectionStats_user_id_idx" ON "CollectionStats"("user_id");

-- CreateIndex
CREATE INDEX "CollectionStats_date_idx" ON "CollectionStats"("date");

-- CreateIndex
CREATE UNIQUE INDEX "CollectionStats_collection_id_user_id_date_key" ON "CollectionStats"("collection_id", "user_id", "date");

-- AddForeignKey
ALTER TABLE "CollectionStats" ADD CONSTRAINT "CollectionStats_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "Collection"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CollectionStats" ADD CONSTRAINT "CollectionStats_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
