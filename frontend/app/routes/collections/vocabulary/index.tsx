import type { Route } from "./+types/index";
import { Navigate } from "react-router";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Collection ${params.id} Vocabulary - Vocab` },
    { name: "description", content: "Vocabulary practice and management" },
  ];
}

export default function VocabularyIndex({ params }: Route.ComponentProps) {
  // Redirect to my-words as the default vocabulary page
  return <Navigate to={`/collections/${params.id}/vocabulary/my-words`} replace />;
}
