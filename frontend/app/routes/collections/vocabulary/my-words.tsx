import type { Route } from "./+types/my-words";
import { MyWordsClient } from "../../../app/collections/[id]/vocabulary/my-words/my-words-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `My Words - Collection ${params.id} - Vocab` },
    { name: "description", content: "View and manage your vocabulary words" },
  ];
}

export default function MyWords({ params }: Route.ComponentProps) {
  return <MyWordsClient params={{ id: params.id }} />;
}
