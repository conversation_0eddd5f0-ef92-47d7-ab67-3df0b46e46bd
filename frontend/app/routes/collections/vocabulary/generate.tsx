import type { Route } from "./+types/generate";
import { GenerateWordsClient } from "../../../app/collections/[id]/vocabulary/generate/generate-words-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Generate Words - Collection ${params.id} - Vocab` },
    { name: "description", content: "Generate new vocabulary words using AI" },
  ];
}

export default function GenerateWords({ params }: Route.ComponentProps) {
  return <GenerateWordsClient params={{ id: params.id }} />;
}
