import type { Route } from "./+types/mcq";
import { McqClient } from "../../../app/collections/[id]/vocabulary/mcq/mcq-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `MCQ Practice - Collection ${params.id} - Vocab` },
    { name: "description", content: "Multiple choice vocabulary practice" },
  ];
}

export default function McqPractice({ params }: Route.ComponentProps) {
  return <McqClient params={{ id: params.id }} />;
}
