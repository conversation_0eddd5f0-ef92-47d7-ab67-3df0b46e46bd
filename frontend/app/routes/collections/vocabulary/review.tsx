import type { Route } from "./+types/review";
import { ReviewClient } from "../../../app/collections/[id]/vocabulary/review/review-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Review Words - Collection ${params.id} - Vocab` },
    { name: "description", content: "Review and practice your vocabulary words" },
  ];
}

export default function ReviewWords({ params }: Route.ComponentProps) {
  return <ReviewClient params={{ id: params.id }} />;
}
