import type { Route } from "./+types/stats";
import { StatsClient } from "../../app/collections/[id]/stats/stats-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Collection ${params.id} Stats - Vocab` },
    { name: "description", content: "Collection statistics and progress" },
  ];
}

export default function CollectionStats({ params }: Route.ComponentProps) {
  return <StatsClient params={{ id: params.id }} />;
}
