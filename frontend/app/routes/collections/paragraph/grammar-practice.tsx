import type { Route } from "./+types/grammar-practice";
import { GrammarPracticeClient } from "../../../app/collections/[id]/paragraph/grammar-practice/grammar-practice-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Grammar Practice - Collection ${params.id} - Vocab` },
    { name: "description", content: "Practice grammar with AI-generated exercises" },
  ];
}

export default function GrammarPractice({ params }: Route.ComponentProps) {
  return <GrammarPracticeClient params={{ id: params.id }} />;
}
