import type { Route } from "./+types/qa-practice";
import { QAPracticeClient } from "../../../app/collections/[id]/paragraph/qa-practice/qa-practice-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Q&A Practice - Collection ${params.id} - Vocab` },
    { name: "description", content: "Practice reading comprehension with questions and answers" },
  ];
}

export default function QAPractice({ params }: Route.ComponentProps) {
  return <QAPracticeClient params={{ id: params.id }} />;
}
