import type { Route } from "./+types/paragraph-practice";
import { ParagraphPracticeClient } from "../../../app/collections/[id]/paragraph/paragraph-practice/paragraph-practice-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Paragraph Practice - Collection ${params.id} - Vocab` },
    { name: "description", content: "Practice reading and translation with paragraphs" },
  ];
}

export default function ParagraphPractice({ params }: Route.ComponentProps) {
  return <ParagraphPracticeClient params={{ id: params.id }} />;
}
