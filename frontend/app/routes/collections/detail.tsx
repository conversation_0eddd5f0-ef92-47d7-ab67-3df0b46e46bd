import type { Route } from "./+types/detail";
import { CollectionOverviewClient } from "../../app/collections/[id]/collection-overview-client";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Collection ${params.id} - Vocab` },
    { name: "description", content: "Collection overview and management" },
  ];
}

export default function CollectionDetail({ params }: Route.ComponentProps) {
  return <CollectionOverviewClient params={{ id: params.id }} />;
}
