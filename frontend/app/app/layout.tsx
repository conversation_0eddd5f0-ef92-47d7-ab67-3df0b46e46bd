'use client';

import './globals.css';
import { ClientSettings } from './components/client-settings';
import { ProgressBar } from '@/components/ui/progress-bar';
import { Toaster } from '@/components/ui/toaster';
import { TranslationProvider } from '@/contexts/translation-context';
import { ThemeProvider } from '@/components/ui/theme-provider';
import { Geist, Geist_Mono } from 'next/font/google';
import { AuthProvider } from '@/contexts/auth-context';
import { MediaQueryProvider } from '@/contexts/media-query-context';
import { LoadingProvider } from '@/contexts/loading-context';
import { AuthGuard } from '@/components/auth/auth-guard';

const geist = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin'],
});

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin'],
});

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<title>Vocab - Smart Language Learning</title>
				<meta
					name="description"
					content="Intelligent vocabulary learning with AI-powered features"
				/>
				<link rel="icon" href="/favicon.ico" />
				<link rel="manifest" href="/manifest.json" />
				<meta name="viewport" content="width=device-width, initial-scale=1" />
				<meta name="theme-color" content="#3b82f6" />
			</head>
			<body className={`${geist.variable} ${geistMono.variable} font-sans antialiased`}>
				<ThemeProvider>
					<AuthProvider>
						<TranslationProvider>
							<MediaQueryProvider>
								<LoadingProvider>
									<AuthGuard>
										<main className="flex-grow px-3 sm:px-9 py-3 sm:py-8">
											<div className="container mx-auto">
												<div className="mb-6 flex flex-col items-center justify-center sm:p-8 md:p-24 max-w-4xl mx-auto">
													{children}
												</div>
											</div>
										</main>
										<ProgressBar />
										<Toaster />
										<ClientSettings />
									</AuthGuard>
								</LoadingProvider>
							</MediaQueryProvider>
						</TranslationProvider>
					</AuthProvider>
				</ThemeProvider>
			</body>
		</html>
	);
}
