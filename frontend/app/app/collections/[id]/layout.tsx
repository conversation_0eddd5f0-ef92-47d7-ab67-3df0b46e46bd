'use client';

import { ReactNode, useEffect, useState } from 'react';
import { CollectionDetailLayoutClient } from './collection-detail-layout-client';
import { CollectionWithDetail } from '@/models';

export default function CollectionDetailLayout({ children }: { children: ReactNode }) {
	const [id, setId] = useState<string>('');

	useEffect(() => {
		if (typeof window !== 'undefined') {
			const pathname = window.location.pathname;
			const pathParts = pathname.split('/');
			const collectionId = pathParts[pathParts.indexOf('collections') + 1];
			setId(collectionId || '');
		}
	}, []);

	// Create a placeholder collection for client-side loading
	const placeholderCollection: CollectionWithDetail = {
		id: id || '',
		name: '',
		user_id: '',
		word_ids: [],
		paragraph_ids: [],
		keyword_ids: [],
		source_language: 'VI',
		target_language: 'EN',
		enable_learn_word_notification: true,
		created_at: new Date(),
		updated_at: new Date(),
		words: [],
	};

	return (
		<CollectionDetailLayoutClient initialCollection={placeholderCollection}>
			{children}
		</CollectionDetailLayoutClient>
	);
}
