'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, Translate } from '@/components/ui';
import { ErrorDisplay } from '@/components/ui/error-display';
import { FeatureCard } from '@/components/ui/feature-card';
import { useCollections } from '@/hooks';
import {
	ArrowRight,
	CheckCircle2,
	Edit3,
	FileText,
	GraduationCap,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Sparkles,
	Target,
	TrendingUp,
	Zap,
} from 'lucide-react';
import { CollectionStatsCard } from './collection-stats-card';

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Collection Info Card Skeleton */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 rounded-2xl animate-pulse" />
				<Card className="relative border-muted/50 bg-background/80 backdrop-blur-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-xl bg-muted/50 animate-pulse">
								<div className="h-8 w-8 bg-muted rounded" />
							</div>
							<div className="h-8 w-80 bg-muted rounded-lg animate-pulse" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
							{[...Array(5)].map((_, i) => (
								<div
									key={i}
									className="p-4 rounded-xl bg-muted/30 border border-muted/50 animate-pulse"
								>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded-lg bg-muted/50">
											<div className="h-5 w-5 bg-muted rounded" />
										</div>
										<div className="flex-1 space-y-2">
											<div className="h-3 w-20 bg-muted rounded" />
											<div className="h-4 w-24 bg-muted rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Feature Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
				{[...Array(3)].map((_, i) => (
					<Card
						key={i}
						className="h-80 border-muted/50 bg-gradient-to-br from-background to-muted/20 animate-pulse"
					>
						<CardHeader className="pb-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-xl bg-muted/50">
										<div className="h-6 w-6 bg-muted rounded" />
									</div>
									<div className="h-6 w-32 bg-muted rounded" />
								</div>
								<div className="h-4 w-4 bg-muted rounded" />
							</div>
							<div className="h-4 w-48 bg-muted rounded mt-3" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[...Array(4)].map((_, j) => (
									<div
										key={j}
										className="flex items-center gap-3 p-3 rounded-lg bg-muted/30"
									>
										<div className="h-4 w-4 bg-muted rounded" />
										<div className="h-4 w-32 bg-muted rounded" />
										<div className="ml-auto h-4 w-4 bg-muted rounded" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();

	const features = currentCollection
		? [
				{
					titleKey: 'collections.tabs.vocabulary',
					descriptionKey: 'collections.overview.vocabulary_desc',
					icon: GraduationCap,
					link: `/collections/${currentCollection.id}/vocabulary`,
					subFeatures: [
						{
							titleKey: 'collections.tabs.generate_words',
							icon: Zap,
							subLink: '/generate',
						},
						{
							titleKey: 'collections.tabs.my_words_list',
							icon: ListChecks,
							subLink: '/my-words',
						},
						{
							titleKey: 'collections.tabs.review',
							icon: RefreshCw,
							subLink: '/review',
						},
						{
							titleKey: 'collections.tabs.multiple_choice_practice',
							icon: Target,
							subLink: '/mcq',
						},
					],
				},
				{
					titleKey: 'collections.tabs.paragraphs',
					descriptionKey: 'collections.overview.paragraphs_desc',
					icon: FileText,
					link: `/collections/${currentCollection.id}/paragraph`,
					subFeatures: [
						{
							titleKey: 'collections.tabs.paragraph_practice',
							icon: Edit3,
							subLink: '/paragraph-practice',
						},
						{
							titleKey: 'qa_practice.tab_title',
							icon: MessageSquare,
							subLink: '/qa-practice',
						},
						{
							titleKey: 'collections.tabs.grammar_practice',
							icon: CheckCircle2,
							subLink: '/grammar-practice',
						},
					],
				},
				{
					titleKey: 'collections.stats.title',
					descriptionKey: 'collections.stats.description',
					icon: TrendingUp,
					link: `/collections/${currentCollection.id}/stats`,
					subFeatures: [],
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />
			<div className="min-h-screen">
				<div className="container mx-auto py-8 space-y-8">
					{/* Hero Section */}
					<div className="text-center space-y-4 mb-12">
						<div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary font-medium text-sm">
							<Sparkles className="h-4 w-4" />
							<Translate text="collections.overview.welcome_back" />
						</div>
						<h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-foreground via-foreground to-foreground/70 bg-clip-text text-transparent">
							<Translate text="collections.overview.dashboard_title" />
						</h1>
						<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
							<Translate text="collections.overview.dashboard_subtitle" />
						</p>
					</div>

					<CollectionStatsCard collection={currentCollection} />

					{/* Features Section */}
					<div className="space-y-6">
						<div className="flex items-center gap-3">
							<h2 className="text-2xl font-bold">
								<Translate text="collections.overview.features_title" />
							</h2>
							<div className="h-px flex-1 bg-gradient-to-r from-border to-transparent" />
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-8">
							{features.map((feature, index) => (
								<div
									key={feature.titleKey}
									className="group animate-in fade-in-0 slide-in-from-bottom-4"
									style={{ animationDelay: `${index * 100}ms` }}
								>
									<FeatureCard
										titleKey={feature.titleKey}
										descriptionKey={feature.descriptionKey}
										icon={feature.icon}
										link={feature.link}
										subFeatures={feature.subFeatures}
									/>
								</div>
							))}
						</div>
					</div>

					{/* Quick Actions */}
					<div className="mt-16 p-8 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-secondary/5 border border-primary/20">
						<div className="text-center space-y-4">
							<h3 className="text-xl font-semibold">
								<Translate text="collections.overview.quick_start_title" />
							</h3>
							<p className="text-muted-foreground">
								<Translate text="collections.overview.quick_start_desc" />
							</p>
							<div className="flex flex-wrap justify-center gap-4 mt-6">
								<a
									href={`/collections/${currentCollection.id}/vocabulary/generate`}
									className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors group"
								>
									<Zap className="h-4 w-4" />
									<Translate text="collections.tabs.generate_words" />
									<ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
								</a>
								<a
									href={`/collections/${currentCollection.id}/vocabulary/review`}
									className="inline-flex items-center gap-2 px-6 py-3 rounded-xl bg-secondary text-secondary-foreground font-medium hover:bg-secondary/80 transition-colors group"
								>
									<RefreshCw className="h-4 w-4" />
									<Translate text="collections.tabs.review" />
									<ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
