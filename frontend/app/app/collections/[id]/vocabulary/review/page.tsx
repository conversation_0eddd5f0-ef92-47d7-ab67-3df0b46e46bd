'use client';

import { ReviewClient } from './review-client';
import { useState, useEffect } from 'react';

export default function ReviewPage() {
	const [id, setId] = useState<string>('');

	useEffect(() => {
		if (typeof window !== 'undefined') {
			const pathname = window.location.pathname;
			const pathParts = pathname.split('/');
			const collectionId = pathParts[pathParts.indexOf('collections') + 1];
			setId(collectionId || '');
		}
	}, []);

	return <ReviewClient params={{ id }} />;
}
