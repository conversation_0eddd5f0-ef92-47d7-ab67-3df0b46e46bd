'use client';

import { useEffect } from 'react';

export default function CollectionVocabularyPage() {
	useEffect(() => {
		if (typeof window !== 'undefined') {
			const pathname = window.location.pathname;
			const pathParts = pathname.split('/');
			const collectionId = pathParts[pathParts.indexOf('collections') + 1];
			if (collectionId) {
				window.location.href = `/collections/${collectionId}/vocabulary/my-words`;
			}
		}
	}, []);

	return null;
}
