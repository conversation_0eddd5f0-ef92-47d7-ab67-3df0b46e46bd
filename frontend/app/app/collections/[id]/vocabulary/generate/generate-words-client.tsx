'use client';

import { generateRandomWordsApi } from '@/backend/api';
import { <PERSON><PERSON>, Loading<PERSON>pinner, Translate, useToast } from '@/components/ui';
import { useKeywordsContext, useTranslation } from '@/contexts';
import { useLLM } from '@/contexts/llm-context';
import { useCollections } from '@/hooks';
import { RandomWord, WordDetail } from '@/models';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { useEffect, useState } from 'react';
import { KeywordForm } from '../../components/keyword-form';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';
import { WordList } from './components/word-list';

export function GenerateWordsClient() {
	const { t } = useTranslation();
	const { toast } = useToast();
	const { generateWordDetails } = useLLM();
	const {
		currentCollection,
		loading: collectionsLoading,
		error: collectionsError,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		refreshCurrentCollection,
	} = useCollections();
	const {
		isLoading: isKeywordsLoading,
		selectedKeywords,
		keywords,
		error: keywordsError,
	} = useKeywordsContext();

	// Simple state management
	const [generatedWords, setGeneratedWords] = useState<RandomWord[]>([]);
	const [detailedWords, setDetailedWords] = useState<Record<string, WordDetail>>({});
	const [isGenerating, setIsGenerating] = useState(false);
	const [wordLoadingStates, setWordLoadingStates] = useState<
		Record<string, { adding: boolean; gettingDetail: boolean }>
	>({});

	// Check if any critical loading is happening
	const isCollectionsLoading = collectionsLoading.get || collectionsLoading.setCurrent;
	const isAnyLoading = isCollectionsLoading || isGenerating || isKeywordsLoading;
	const hasErrors = !!(collectionsError || keywordsError);

	// Error notifications
	useEffect(() => {
		if (collectionsError) {
			toast({
				variant: 'destructive',
				title: t('collections.error'),
				description: collectionsError.message,
			});
		}
		if (keywordsError) {
			toast({
				variant: 'destructive',
				title: t('keywords.error'),
				description: keywordsError.message,
			});
		}
	}, [collectionsError, keywordsError, t, toast]);

	// Collection safety check
	if (!currentCollection) return null;

	// Generate words function
	const generateWords = async (keywords: string[]) => {
		setIsGenerating(true);
		try {
			const result = await generateRandomWordsApi(
				keywords,
				10,
				[currentCollection.id],
				currentCollection.source_language,
				currentCollection.target_language
			);
			setGeneratedWords(result);
		} catch (error) {
			const err = error instanceof Error ? error : new Error('Failed to generate words');
			toast({
				variant: 'destructive',
				title: t('words.generation_failed'),
				description: err.message,
			});
		} finally {
			setIsGenerating(false);
		}
	};

	// Get word details
	const handleGetDetails = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.gettingDetail || detailedWords[word.term]) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], gettingDetail: true },
		}));

		try {
			const detailsList = await generateWordDetails(
				[word.term],
				currentCollection.source_language,
				currentCollection.target_language
			);
			if (detailsList && detailsList.length > 0) {
				const wordDetail = detailsList[0] as WordDetail;
				setDetailedWords((prev) => ({ ...prev, [word.term]: wordDetail }));
			} else {
				throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
			}
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.detail_fetch_error'),
				description: err.message,
			});
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], gettingDetail: false },
			}));
		}
	};

	// Add word to collection
	const handleAddToCollection = async (word: RandomWord) => {
		if (wordLoadingStates[word.term]?.adding) return;

		setWordLoadingStates((prev) => ({
			...prev,
			[word.term]: { ...prev[word.term], adding: true },
		}));

		try {
			if (detailedWords[word.term]?.id) {
				await addWordsToCurrentCollection([detailedWords[word.term].id]);
			} else {
				await addTermToCurrentCollection(word.term, currentCollection.target_language);
			}
			toast({
				title: t('words.word_added'),
				description: t('words.word_added_desc', { term: word.term }),
			});
			await refreshCurrentCollection();
			setWordLoadingStates((prev) => {
				const { [word.term]: _, ...rest } = prev;
				return rest;
			});
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.add_error'),
				description: t('words.add_error_desc', {
					term: word.term,
					message: err.message,
				}),
			});
		} finally {
			setWordLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], adding: false },
			}));
		}
	};

	// Show loading skeleton while any critical data is loading
	if (isAnyLoading && !currentCollection) return <PracticeSessionSkeleton type="paragraph" />;

	// Handle generate button click
	const handleGenerate = async () => {
		if (selectedKeywords.length === 0 || isAnyLoading || hasErrors) return;

		const keywordNames = selectedKeywords
			.map((id) => {
				const keyword = keywords.find((k) => k.id === id);
				return keyword?.content || '';
			})
			.filter(Boolean);

		if (keywordNames.length === 0) {
			toast({
				variant: 'destructive',
				title: t('keywords.error'),
				description: t('keywords.no_valid_selected'),
			});
			return;
		}

		await generateWords(keywordNames);
	};

	// Helper function to get word loading state
	const getWordLoadingState = (term: string) => {
		return wordLoadingStates[term] || { adding: false, gettingDetail: false };
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="words.generate_words" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="words.generate_description" />
					</p>
				</header>

				<section className="space-y-4">
				<KeywordForm />

				{/* Generate button */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.3 }}
				>
					<div className="space-y-3">
						{selectedKeywords.length > 0 && (
							<div className="text-sm text-primary/80 font-medium">
								<Translate
									text="words.selected_count"
									values={{ count: selectedKeywords.length }}
								/>
							</div>
						)}
						<motion.div whileTap={{ scale: 0.98 }}>
							<Button
								className="w-full h-12 text-base font-bold rounded-2xl bg-primary text-background shadow-2xl hover:bg-primary/90 transition-all duration-200 flex gap-3 items-center justify-center"
								disabled={
									selectedKeywords.length === 0 || isAnyLoading || hasErrors
								}
								onClick={handleGenerate}
								size="sm"
								loading={isGenerating || isKeywordsLoading}
							>
								{isGenerating || isKeywordsLoading ? (
									<>
										<LoadingSpinner size="sm" />
										<Translate
											text={isGenerating ? 'words.generating' : 'ui.loading'}
										/>
									</>
								) : (
									<>
										<Sparkles className="h-6 w-6 animate-bounce" />
										<Translate text="words.generate_words" />
									</>
								)}
							</Button>
						</motion.div>
					</div>
				</motion.div>

				<div className="grid grid-cols-1 gap-4">
					{/* Show loading state for word list operations */}
					{isGenerating ? (
						<div className="flex items-center justify-center p-8 border-2 border-dashed border-muted-foreground/20 rounded-lg">
							<div className="flex flex-col items-center space-y-3">
								<LoadingSpinner size="lg" />
								<p className="text-muted-foreground">
									<Translate text="words.generating_please_wait" />
								</p>
							</div>
						</div>
					) : (
						<div className={`relative`}>
							<WordList
								words={generatedWords}
								detailedWords={detailedWords}
								onGetDetails={handleGetDetails}
								getLoadingState={getWordLoadingState}
								onAddToCollection={handleAddToCollection}
								className="mt-6"
								sourceLanguage={currentCollection.source_language}
								targetLanguage={currentCollection.target_language}
							/>
						</div>
					)}
				</div>
			</section>
			</div>
		</div>
	);
}
