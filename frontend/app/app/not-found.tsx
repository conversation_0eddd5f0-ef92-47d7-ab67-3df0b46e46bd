'use client';

import { Button, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { Home, Search, ArrowLeft } from 'lucide-react';
import { ReactNode, useEffect, useState } from 'react';

// Glitch and floating word animation constants
const VOCABULARY_WORDS = [
	'learn',
	'study',
	'practice',
	'knowledge',
	'language',
	'words',
	'memory',
	'fluent',
];

const getRandomPositions = (words: string[]) => {
	return words.map(() => ({
		left: Math.random() * 80 + 10, // 10% to 90% from left
		xOffset: (Math.random() - 0.5) * 100, // -50 to 50 pixels
		xAnimate: (Math.random() - 0.5) * 200, // -100 to 100 pixels
		rotation: (Math.random() - 0.5) * 30, // -15 to 15 degrees
		repeatDelay: Math.random() * 3 + 1, // 1 to 4 seconds
	}));
};

const FloatingWord = ({
	word,
	delay,
	position,
}: {
	word: string;
	delay: number;
	position: {
		left: number;
		xOffset: number;
		xAnimate: number;
		rotation: number;
		repeatDelay: number;
	};
}) => {
	return (
		<motion.div
			className="absolute text-primary/20 dark:text-primary/30 font-medium select-none pointer-events-none"
			style={{
				left: `${position.left}%`,
				fontSize: `${Math.random() * 12 + 10}px`,
			}}
			initial={{
				y: '100vh',
				x: position.xOffset,
				rotate: position.rotation,
				opacity: 0,
			}}
			animate={{
				y: '-20vh',
				x: position.xOffset + position.xAnimate,
				rotate: position.rotation + (Math.random() - 0.5) * 60,
				opacity: [0, 0.8, 0.8, 0],
			}}
			transition={{
				duration: 8 + Math.random() * 4,
				delay: delay,
				repeat: Infinity,
				repeatDelay: position.repeatDelay,
				ease: 'linear',
				opacity: {
					times: [0, 0.2, 0.8, 1],
					duration: 8 + Math.random() * 4,
				},
			}}
		>
			{word}
		</motion.div>
	);
};

const GlitchText = ({ children }: { children: ReactNode }) => {
	return (
		<motion.div
			className="relative inline-block"
			animate={{
				textShadow: [
					'0 0 0 transparent',
					'2px 0 0 #ff00ff, -2px 0 0 #00ffff',
					'0 0 0 transparent',
					'1px 0 0 #ff00ff, -1px 0 0 #00ffff',
					'0 0 0 transparent',
				],
			}}
			transition={{
				duration: 0.5,
				repeat: Infinity,
				repeatDelay: 3,
				ease: 'easeInOut',
			}}
		>
			{children}
		</motion.div>
	);
};

export default function NotFound() {
	const { t } = useTranslation();
	const [wordPositions, setWordPositions] = useState<
		{
			left: number;
			xOffset: number;
			xAnimate: number;
			rotation: number;
			repeatDelay: number;
		}[]
	>([]);

	useEffect(() => {
		setWordPositions(getRandomPositions(VOCABULARY_WORDS));
	}, []);

	return (
		<div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 relative overflow-hidden">
			{/* Floating vocabulary words */}
			{VOCABULARY_WORDS.map((word, index) => (
				<FloatingWord
					key={word}
					word={word}
					delay={index * 0.5}
					position={wordPositions[index] || getRandomPositions([word])[0]}
				/>
			))}

			{/* Main Content */}
			<div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center">
				<motion.div
					initial={{ opacity: 0, y: -50 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, ease: 'easeOut' }}
					className="space-y-8"
				>
					{/* Giant 404 with glitch effect */}
					<div className="text-9xl md:text-[12rem] font-bold text-primary/20 dark:text-primary/30 leading-none select-none">
						<GlitchText>404</GlitchText>
					</div>

					{/* Error Message */}
					<div className="space-y-4">
						<h1 className="text-4xl md:text-6xl font-bold text-foreground">
							<Translate text="errors.page_not_found_title" />
						</h1>
						<p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
							<Translate text="errors.page_not_found_description" />
						</p>
					</div>

					{/* Action Buttons */}
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.4 }}
						className="flex flex-col sm:flex-row gap-4 justify-center items-center"
					>
						<Button
							size="lg"
							className="flex items-center gap-2 px-8 py-3 text-lg rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-200"
							onClick={() => (window.location.href = '/')}
						>
							<Home className="h-5 w-5" />
							<Translate text="nav.home" />
						</Button>

						<Button
							variant="outline"
							size="lg"
							className="flex items-center gap-2 px-8 py-3 text-lg rounded-xl border-2 hover:bg-muted transition-all duration-200"
							onClick={() => window.history.back()}
						>
							<ArrowLeft className="h-5 w-5" />
							<Translate text="ui.go_back" />
						</Button>
					</motion.div>

					{/* Additional Help */}
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.6, delay: 0.8 }}
						className="pt-8 border-t border-border/50 max-w-md mx-auto"
					>
						<p className="text-sm text-muted-foreground mb-4">
							<Translate text="errors.page_not_found_help" />
						</p>
						<div className="flex justify-center">
							<Button
								variant="ghost"
								size="sm"
								className="flex items-center gap-2 text-primary hover:text-primary/80"
								onClick={() => (window.location.href = '/collections')}
							>
								<Search className="h-4 w-4" />
								<Translate text="collections.browse_collections" />
							</Button>
						</div>
					</motion.div>
				</motion.div>
			</div>
		</div>
	);
}
