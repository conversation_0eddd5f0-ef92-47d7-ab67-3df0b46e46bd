// ============================================================================
// LOADING KEYS CONSTANTS
// ============================================================================
// Centralized loading keys for consistent usage across the application

// Auth Context Loading Keys
export const AUTH_LOADING_KEYS = {
	GET_USER: 'getUser',
	PROVIDER_LOGIN: 'providerLogin',
	LOGOUT: 'logout',
	GET_USER_BY_PROVIDER: 'getUserByProvider',
} as const;

// Keywords Context Loading Keys
export const KEYWORDS_LOADING_KEYS = {
	FETCH_KEYWORDS: 'fetchKeywords',
	GET_KEYWORD: 'getKeyword',
	SEARCH_KEYWORDS: 'searchKeywords',
	CREATE_KEYWORD: 'createKeyword',
	UPDATE_KEYWORD: 'updateKeyword',
	DELETE_KEYWORD: 'deleteKeyword',
} as const;

// Collections Context Loading Keys
export const COLLECTIONS_LOADING_KEYS = {
	FETCH: 'fetch',
	GET: 'get',
	GET_WORDS_TO_REVIEW: 'getWordsToReview',
	SEARCH: 'search',
	CREATE: 'create',
	UPDATE: 'update',
	DELETE: 'delete',
	ADD_TERM: 'addTerm',
	ADD_WORDS: 'addWords',
	REMOVE_WORDS: 'removeWords',
	SET_CURRENT: 'setCurrent',
	WORDS_SEARCH: 'wordsSearch',
	FETCH_WORDS: 'fetchWords',
	GET_WORDS_TO_REVIEW_WORDS: 'getWordsToReviewWords',
	BULK_DELETE_WORDS: 'bulkDeleteWords',
	FETCH_WORD: 'fetchWord',
} as const;

// LLM Context Loading Keys
export const LLM_LOADING_KEYS = {
	GENERATE_RANDOM_TERMS: 'generateRandomTerms',
	GENERATE_WORD_DETAILS: 'generateWordDetails',
	GENERATE_PARAGRAPHS: 'generateParagraphs',
	EVALUATE_TRANSLATION: 'evaluateTranslation',
	GENERATE_QUESTIONS: 'generateQuestions',
	GENERATE_PARAGRAPH_WITH_QUESTIONS: 'generateParagraphWithQuestions',
	EVALUATE_ANSWERS: 'evaluateAnswers',
	GENERATE_GRAMMAR_PRACTICE: 'generateGrammarPractice',
} as const;

// Translation Context Loading Keys
export const TRANSLATION_LOADING_KEYS = {
	// Add specific translation loading keys if any
} as const;

// Last Seen Word Context Loading Keys
export const LAST_SEEN_WORD_LOADING_KEYS = {
	// Add specific last seen word loading keys if any
} as const;

// Component-specific Loading Keys
export const FEEDBACK_SECTION_LOADING_KEYS = {
	SUBMIT_FEEDBACK: 'submitFeedback',
} as const;

export const KEYWORD_FORM_LOADING_KEYS = {
	CREATE_KEYWORD: 'createKeyword',
} as const;

export const OPTIMIZED_IMAGE_LOADING_KEYS = {
	// Add specific optimized image loading keys if any
} as const;

// Export all loading keys for easy access
export const LOADING_KEYS = {
	AUTH: AUTH_LOADING_KEYS,
	KEYWORDS: KEYWORDS_LOADING_KEYS,
	COLLECTIONS: COLLECTIONS_LOADING_KEYS,
	LLM: LLM_LOADING_KEYS,
	TRANSLATION: TRANSLATION_LOADING_KEYS,
	LAST_SEEN_WORD: LAST_SEEN_WORD_LOADING_KEYS,
	FEEDBACK_SECTION: FEEDBACK_SECTION_LOADING_KEYS,
	KEYWORD_FORM: KEYWORD_FORM_LOADING_KEYS,
	OPTIMIZED_IMAGE: OPTIMIZED_IMAGE_LOADING_KEYS,
} as const;

// Type definitions for better TypeScript support
export type AuthLoadingKey = (typeof AUTH_LOADING_KEYS)[keyof typeof AUTH_LOADING_KEYS];
export type KeywordsLoadingKey = (typeof KEYWORDS_LOADING_KEYS)[keyof typeof KEYWORDS_LOADING_KEYS];
export type CollectionsLoadingKey =
	(typeof COLLECTIONS_LOADING_KEYS)[keyof typeof COLLECTIONS_LOADING_KEYS];
export type LLMLoadingKey = (typeof LLM_LOADING_KEYS)[keyof typeof LLM_LOADING_KEYS];
export type TranslationLoadingKey =
	(typeof TRANSLATION_LOADING_KEYS)[keyof typeof TRANSLATION_LOADING_KEYS];
export type LastSeenWordLoadingKey =
	(typeof LAST_SEEN_WORD_LOADING_KEYS)[keyof typeof LAST_SEEN_WORD_LOADING_KEYS];
export type FeedbackSectionLoadingKey =
	(typeof FEEDBACK_SECTION_LOADING_KEYS)[keyof typeof FEEDBACK_SECTION_LOADING_KEYS];
export type KeywordFormLoadingKey =
	(typeof KEYWORD_FORM_LOADING_KEYS)[keyof typeof KEYWORD_FORM_LOADING_KEYS];
export type OptimizedImageLoadingKey =
	(typeof OPTIMIZED_IMAGE_LOADING_KEYS)[keyof typeof OPTIMIZED_IMAGE_LOADING_KEYS];
