// ============================================================================
// LOADING SCOPES CONSTANTS
// ============================================================================
// Centralized loading scope names for consistent usage across the application

// Context Scopes
export const LOADING_SCOPES = {
	AUTH: 'auth',
	KEYWORDS: 'keywords',
	COLLECTIONS: 'collections',
	LLM: 'llm',
	TRANSLATION: 'translation',
	LAST_SEEN_WORD: 'lastSeenWord',
	// Component Scopes
	FEEDBACK_SECTION: 'feedback-section',
	KEYWORD_FORM: 'keyword-form',
	OPTIMIZED_IMAGE: 'optimized-image',
} as const;

// Type definition for better TypeScript support
export type LoadingScope = typeof LOADING_SCOPES[keyof typeof LOADING_SCOPES];