'use client';

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// Theme handling utils
export type Theme = 'light' | 'dark' | 'system';

export function getSystemTheme(): Theme {
	if (typeof window !== 'undefined') {
		return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
	}
	return 'light'; // Default to light if SSR
}

// Function to set the theme in localStorage and apply it to the document
export function setTheme(theme: Theme) {
	if (typeof window === 'undefined') return;

	// Save theme preference
	localStorage.setItem('theme', theme);

	// Apply theme
	const isDark = theme === 'dark' || (theme === 'system' && getSystemTheme() === 'dark');

	document.documentElement.classList.toggle('dark', isDark);
}

// Function to get the current theme from localStorage
export function getTheme(): Theme {
	if (typeof window === 'undefined') return 'system';
	return (localStorage.getItem('theme') as Theme) || 'system';
}
