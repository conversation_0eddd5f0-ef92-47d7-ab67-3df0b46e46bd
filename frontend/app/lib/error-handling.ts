export class AppError extends <PERSON>rror {
	constructor(
		message: string,
		public readonly code: string,
		public readonly statusCode: number = 500
	) {
		super(message);
		this.name = 'AppError';
	}
}

export class ValidationError extends AppError {
	constructor(message: string) {
		super(message, 'VALIDATION_ERROR', 400);
		this.name = 'ValidationError';
	}
}

export class NotFoundError extends AppError {
	constructor(resource: string, id: string) {
		super(`${resource} with id ${id} not found`, 'NOT_FOUND', 404);
		this.name = 'NotFoundError';
	}
}

export class UnauthorizedError extends AppError {
	constructor(message: string = 'Unauthorized access') {
		super(message, 'UNAUTHORIZED', 401);
		this.name = 'UnauthorizedError';
	}
}

export class ForbiddenError extends AppError {
	constructor(message: string = 'Access forbidden') {
		super(message, 'FORBIDDEN', 403);
		this.name = 'ForbiddenError';
	}
}
