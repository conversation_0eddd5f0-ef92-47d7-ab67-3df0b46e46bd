'use client';

import {
	evaluateA<PERSON>wers<PERSON><PERSON>,
	evaluateTransla<PERSON><PERSON><PERSON>,
	generate<PERSON>rammarPractice<PERSON>pi,
	generateParagraph<PERSON><PERSON>,
	generateParagraphWithQuestionsApi,
	generateQuestionsApi,
	generateRandomWordsApi,
	generateWordDetailsApi,
} from '@/backend/api';
import {
	AnswerEvaluationResult,
	EvaluateAnswersParams,
	EvaluateTranslationParams,
	GenerateQuestionsParams,
	GenerateParagraphWithQuestionsParams,
	ParagraphWithQuestionsResult,
	GrammarPracticeParams,
	GrammarPracticeResultItem,
} from '@/backend/services';
import { LLM_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { RandomWord, WordDetail } from '@/models/word';
import { Difficulty, Language } from '@prisma/client';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type LLMContextType = {
	isLoading: boolean;
	error: Error | null;
	generateRandomTerms: (data: {
		keywords: string[];
		max_terms: number;
		exclude_collection_ids: string[];
		source_language: Language;
		target_language: Language;
	}) => Promise<RandomWord[]>;
	generateWordDetails: (
		terms: string[],
		source_language: Language,
		target_language: Language
	) => Promise<WordDetail[]>;
	generateParagraphs: (data: {
		keywords: string[];
		language: Language;
		difficulty: Difficulty;
		count: number;
		sentenceCount?: number;
	}) => Promise<string[]>;
	evaluateTranslation: (params: EvaluateTranslationParams) => Promise<any>;
	generateQuestions: (params: GenerateQuestionsParams) => Promise<string[]>;
	generateParagraphWithQuestions: (
		params: GenerateParagraphWithQuestionsParams
	) => Promise<ParagraphWithQuestionsResult>;
	evaluateAnswers: (params: EvaluateAnswersParams) => Promise<AnswerEvaluationResult[]>;
	generateGrammarPractice: (
		params: GrammarPracticeParams
	) => Promise<GrammarPracticeResultItem[]>;
	clearError: () => void;
	getLoadingState: (key: string) => boolean;
};

const LLMContext = createContext<LLMContextType | undefined>(undefined);

export function LLMProvider({ children }: { children: React.ReactNode }) {
	const [error, setError] = useState<Error | null>(null);

	const { getLoading } = useScopedLoading(LOADING_SCOPES.LLM);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.LLM);

	const isLoading =
		getLoading(LLM_LOADING_KEYS.GENERATE_RANDOM_TERMS) ||
		getLoading(LLM_LOADING_KEYS.GENERATE_WORD_DETAILS) ||
		getLoading(LLM_LOADING_KEYS.GENERATE_PARAGRAPHS) ||
		getLoading(LLM_LOADING_KEYS.EVALUATE_TRANSLATION) ||
		getLoading(LLM_LOADING_KEYS.GENERATE_QUESTIONS) ||
		getLoading(LLM_LOADING_KEYS.GENERATE_PARAGRAPH_WITH_QUESTIONS) ||
		getLoading(LLM_LOADING_KEYS.EVALUATE_ANSWERS) ||
		getLoading(LLM_LOADING_KEYS.GENERATE_GRAMMAR_PRACTICE);

	const generateRandomTerms = useCallback(
		async (data: {
			keywords: string[];
			max_terms: number;
			exclude_collection_ids: string[];
			source_language: Language;
			target_language: Language;
		}) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				LLM_LOADING_KEYS.GENERATE_RANDOM_TERMS
			);
			start();
			try {
				const result = await generateRandomWordsApi(
					data.keywords,
					data.max_terms,
					data.exclude_collection_ids,
					data.source_language,
					data.target_language
				);
				end();
				return result as RandomWord[];
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate random terms');
				end(error);
				return [] as RandomWord[];
			}
		},
		[]
	);

	const generateWordDetails = useCallback(
		async (terms: string[], source_language: Language, target_language: Language) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				LLM_LOADING_KEYS.GENERATE_WORD_DETAILS
			);
			start();
			try {
				const result = await generateWordDetailsApi(
					terms,
					source_language,
					target_language
				);
				end();
				return result as WordDetail[];
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate word details');
				end(error);
				return [] as WordDetail[];
			}
		},
		[]
	);

	const generateParagraphs = useCallback(
		async (data: {
			keywords: string[];
			language: Language;
			difficulty: Difficulty;
			count: number;
			sentenceCount?: number;
		}) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				LLM_LOADING_KEYS.GENERATE_PARAGRAPHS
			);
			start();
			try {
				const result = await generateParagraphApi(
					data.keywords,
					data.language,
					data.difficulty,
					data.count,
					data.sentenceCount
				);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate paragraphs');
				end(error);
				return [] as string[];
			}
		},
		[]
	);

	const evaluateTranslation = useCallback(async (params: EvaluateTranslationParams) => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			LLM_LOADING_KEYS.EVALUATE_TRANSLATION
		);
		start();
		try {
			const result = await evaluateTranslationApi(params);
			end();
			return result;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to evaluate translation');
			end(error);
			return null;
		}
	}, []);

	const generateQuestions = useCallback(async (params: GenerateQuestionsParams) => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			LLM_LOADING_KEYS.GENERATE_QUESTIONS
		);
		start();
		try {
			const result = await generateQuestionsApi(params);
			end();
			return result;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to generate questions');
			end(error);
			return [] as string[];
		}
	}, []);

	const generateParagraphWithQuestions = useCallback(
		async (
			params: GenerateParagraphWithQuestionsParams
		): Promise<ParagraphWithQuestionsResult> => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				LLM_LOADING_KEYS.GENERATE_PARAGRAPH_WITH_QUESTIONS
			);
			start();
			try {
				const result = await generateParagraphWithQuestionsApi(params);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error
						? err
						: new Error('Failed to generate paragraph with questions');
				end(error);
				return { paragraph: '', questions: [] } as ParagraphWithQuestionsResult;
			}
		},
		[]
	);

	const evaluateAnswers = useCallback(
		async (params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				LLM_LOADING_KEYS.EVALUATE_ANSWERS
			);
			start();
			try {
				const result = await evaluateAnswersApi(params);
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to evaluate answers');
				end(error);
				return [] as AnswerEvaluationResult[];
			}
		},
		[]
	);

	const generateGrammarPractice = useCallback(
		async (params: GrammarPracticeParams): Promise<GrammarPracticeResultItem[]> => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				LLM_LOADING_KEYS.GENERATE_GRAMMAR_PRACTICE
			);
			start();
			try {
				const result = await generateGrammarPracticeApi(params);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate grammar practice');
				end(error);
				return [] as GrammarPracticeResultItem[];
			}
		},
		[]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const value = useMemo(
		() => ({
			isLoading,
			error,
			generateRandomTerms,
			generateWordDetails,
			generateParagraphs,
			evaluateTranslation,
			generateQuestions,
			generateParagraphWithQuestions,
			evaluateAnswers,
			generateGrammarPractice,
			clearError,
			getLoadingState: getLoading,
		}),
		[isLoading, error]
	);

	return <LLMContext.Provider value={value}>{children}</LLMContext.Provider>;
}

export function useLLMContext() {
	const context = useContext(LLMContext);
	if (context === undefined) throw new Error('useLLMContext must be used within a LLMProvider');
	return context;
}

export function useLLM() {
	return useLLMContext();
}
