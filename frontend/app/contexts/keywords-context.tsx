'use client';

import {
	create<PERSON><PERSON><PERSON><PERSON><PERSON>,
	delete<PERSON><PERSON><PERSON><PERSON><PERSON>,
	getAll<PERSON>eywordsOfUser<PERSON><PERSON>,
	getK<PERSON>word<PERSON><PERSON>,
	update<PERSON><PERSON>word<PERSON><PERSON>,
} from '@/backend/api';
import { KEYWORDS_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { KeywordWithDetail } from '@/models';
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type KeywordsContextType = {
	keywords: KeywordWithDetail[];
	isLoading: boolean;
	error: Error | null;
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	fetchKeywords: () => Promise<void>;
	getKeyword: (id: string) => Promise<KeywordWithDetail | null>;
	searchKeywords: (term: string) => Promise<void>;
	createKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	updateKeyword: (id: string, name: string) => Promise<KeywordWithDetail | null>;
	deleteKeyword: (id: string) => Promise<void>;
	getLoadingState: (key: string) => boolean;
};

const KeywordsContext = createContext<KeywordsContextType | undefined>(undefined);

export function KeywordsProvider({ children }: { children: React.ReactNode }) {
	const [keywords, setKeywords] = useState<KeywordWithDetail[]>([]);
	const [error, setError] = useState<Error | null>(null);
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const { getLoading } = useScopedLoading(LOADING_SCOPES.KEYWORDS);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.KEYWORDS);

	// Computed loading state from any keywords operation
	const isLoading =
		getLoading(KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS) ||
		getLoading(KEYWORDS_LOADING_KEYS.GET_KEYWORD) ||
		getLoading(KEYWORDS_LOADING_KEYS.SEARCH_KEYWORDS) ||
		getLoading(KEYWORDS_LOADING_KEYS.CREATE_KEYWORD) ||
		getLoading(KEYWORDS_LOADING_KEYS.UPDATE_KEYWORD) ||
		getLoading(KEYWORDS_LOADING_KEYS.DELETE_KEYWORD);

	const fetchKeywords = useCallback(async () => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS
		);
		start();
		try {
			const result = await getAllKeywordsOfUserApi();
			setKeywords(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to fetch keywords');
			end(error);
		}
	}, [loadingErrorHelper]);

	const getKeyword = useCallback(
		async (id: string): Promise<KeywordWithDetail | null> => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				KEYWORDS_LOADING_KEYS.GET_KEYWORD
			);
			start();
			try {
				const result = await getKeywordApi(id);
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to fetch keyword');
				end(error);
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const searchKeywords = useCallback(
		async (term: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				KEYWORDS_LOADING_KEYS.SEARCH_KEYWORDS
			);
			start();
			try {
				const result = await getAllKeywordsOfUserApi();
				const filtered = result.filter((keyword) =>
					keyword.content.toLowerCase().includes(term.toLowerCase())
				);
				setKeywords(filtered);
				end();
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to search keywords');
				end(error);
			}
		},
		[loadingErrorHelper]
	);

	const createKeyword = useCallback(
		async (name: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				KEYWORDS_LOADING_KEYS.CREATE_KEYWORD
			);
			start();
			try {
				const result = await createKeywordApi({ name });
				await fetchKeywords();
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to create keyword');
				end(error);
				return null;
			}
		},
		[fetchKeywords, loadingErrorHelper]
	);

	const updateKeyword = useCallback(
		async (id: string, name: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				KEYWORDS_LOADING_KEYS.UPDATE_KEYWORD
			);
			start();
			try {
				const result = await updateKeywordApi(id, { name, description: '' });
				await fetchKeywords();
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to update keyword');
				end(error);
				return null;
			}
		},
		[fetchKeywords, loadingErrorHelper]
	);

	const deleteKeyword = useCallback(
		async (id: string) => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				KEYWORDS_LOADING_KEYS.DELETE_KEYWORD
			);
			start();
			try {
				await deleteKeywordApi(id);
				await fetchKeywords();
				setSelectedKeywords((prev) => prev.filter((keywordId) => keywordId !== id));
				end();
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to delete keyword');
				end(error);
			}
		},
		[fetchKeywords, loadingErrorHelper]
	);

	const isFirstLoad = useRef(true);
	useEffect(() => {
		if (isFirstLoad.current) {
			isFirstLoad.current = false;
			fetchKeywords();
		}
	}, [fetchKeywords]);

	const value = useMemo(
		() => ({
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywords,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoadingState: getLoading,
		}),
		[
			keywords,
			isLoading,
			error,
			selectedKeywords,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoading,
		]
	);

	return <KeywordsContext.Provider value={value}>{children}</KeywordsContext.Provider>;
}

export function useKeywordsContext() {
	const context = useContext(KeywordsContext);
	if (context === undefined)
		throw new Error('useKeywordsContext must be used within a KeywordsProvider');
	return context;
}
