'use client';

import { COLLECTIONS_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { getAllCollections, saveCollections } from '@/lib/indexed-db/collection-indexed-db';
import { CollectionWithDetail, WordDetail } from '@/models';
import {
	createContext,
	Dispatch,
	ReactNode,
	SetStateAction,
	useCallback,
	useEffect,
	useMemo,
	useState,
} from 'react';
import { useScopedLoading } from './loading-context';

type LoadingState = {
	fetch: boolean; // Loading collections list
	get: boolean; // Loading collection details
	getWordsToReview: boolean; // Loading words to review list
	search: boolean; // Searching collections
	create: boolean; // Creating new collection
	update: boolean; // Updating collection
	delete: boolean; // Deleting collection
	addTerm: boolean; // Adding term to collection
	addWords: boolean; // Adding words to collection
	removeWords: boolean; // Removing words from collection
	setCurrent: boolean; // Setting current collection

	// Words loading states
	wordsSearch: boolean; // Searching words
	fetchWords: boolean; // Fetching words by collection
	getWordsToReviewWords: boolean; // Getting words to review
	bulkDeleteWords: boolean; // Bulk deleting words
	fetchWord: boolean; // Fetching individual word
};

// Context type definition
export type CollectionsContextType = {
	// Collections state
	collections: CollectionWithDetail[];
	setCollections: Dispatch<SetStateAction<CollectionWithDetail[]>>;
	currentCollection: CollectionWithDetail | null;
	setCurrentCollection: Dispatch<SetStateAction<CollectionWithDetail | null>>;
	loading: LoadingState;
	error: Error | null;
	setError: Dispatch<SetStateAction<Error | null>>;
	currentCollectionWords: WordDetail[]; // Words specifically for current collection
	setCurrentCollectionWords: Dispatch<SetStateAction<WordDetail[]>>;
};

const CollectionsContext = createContext<CollectionsContextType | undefined>(undefined);

export function CollectionsProvider({
	children,
	initialCollections,
}: {
	children: ReactNode;
	initialCollections?: CollectionWithDetail[];
}) {
	const [collections, setCollections] = useState<CollectionWithDetail[]>(
		initialCollections || []
	);
	const [currentCollection, setCurrentCollection] = useState<CollectionWithDetail | null>(null);
	const [error, setError] = useState<Error | null>(null);
	const [isInitialized, setIsInitialized] = useState(false);

	// Words state - unified for both general and current collection
	const [currentCollectionWords, setCurrentCollectionWords] = useState<WordDetail[]>([]);
	const { getLoading } = useScopedLoading(LOADING_SCOPES.COLLECTIONS);

	const loadCollectionsFromDB = useCallback(async () => {
		try {
			const savedCollections = await getAllCollections();
			if (!initialCollections) {
				setCollections(savedCollections);
			}
		} catch (err) {
			console.error('Failed to load collections from IndexedDB:', err);
			setError(err as Error);
		}
	}, [initialCollections]);

	useEffect(() => {
		if (!isInitialized) {
			loadCollectionsFromDB();
			setIsInitialized(true);
		}
	}, [isInitialized, loadCollectionsFromDB]);

	useEffect(() => {
		saveCollections(collections);
	}, [collections]);

	// Create a loading state object for backward compatibility
	const loading: LoadingState = useMemo(
		() => ({
			fetch: getLoading(COLLECTIONS_LOADING_KEYS.FETCH),
			get: getLoading(COLLECTIONS_LOADING_KEYS.GET),
			getWordsToReview: getLoading(COLLECTIONS_LOADING_KEYS.GET_WORDS_TO_REVIEW),
			search: getLoading(COLLECTIONS_LOADING_KEYS.SEARCH),
			create: getLoading(COLLECTIONS_LOADING_KEYS.CREATE),
			update: getLoading(COLLECTIONS_LOADING_KEYS.UPDATE),
			delete: getLoading(COLLECTIONS_LOADING_KEYS.DELETE),
			addTerm: getLoading(COLLECTIONS_LOADING_KEYS.ADD_TERM),
			addWords: getLoading(COLLECTIONS_LOADING_KEYS.ADD_WORDS),
			removeWords: getLoading(COLLECTIONS_LOADING_KEYS.REMOVE_WORDS),
			setCurrent: getLoading(COLLECTIONS_LOADING_KEYS.SET_CURRENT),
			wordsSearch: getLoading(COLLECTIONS_LOADING_KEYS.WORDS_SEARCH),
			fetchWords: getLoading(COLLECTIONS_LOADING_KEYS.FETCH_WORDS),
			getWordsToReviewWords: getLoading(COLLECTIONS_LOADING_KEYS.GET_WORDS_TO_REVIEW_WORDS),
			bulkDeleteWords: getLoading(COLLECTIONS_LOADING_KEYS.BULK_DELETE_WORDS),
			fetchWord: getLoading(COLLECTIONS_LOADING_KEYS.FETCH_WORD),
		}),
		[getLoading]
	);

	const contextValue = useMemo<CollectionsContextType>(
		() => ({
			collections,
			setCollections,
			currentCollection,
			setCurrentCollection,

			loading,
			error,
			setError,

			currentCollectionWords,
			setCurrentCollectionWords,
		}),
		[
			collections,
			setCollections,
			currentCollection,
			setCurrentCollection,

			loading,
			error,
			setError,

			currentCollectionWords,
			setCurrentCollectionWords,
		]
	);

	return (
		<CollectionsContext.Provider value={contextValue}>{children}</CollectionsContext.Provider>
	);
}

export { CollectionsContext };
