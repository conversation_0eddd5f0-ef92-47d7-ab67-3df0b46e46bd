import {
	type RouteConfig,
	index,
	route,
	layout,
} from "@react-router/dev/routes";

export default [
	// Home page
	index("routes/home.tsx"),

	// Login page
	route("/login", "routes/login.tsx"),

	// Collections routes
	route("/collections", "routes/collections.tsx"),

	// Collection detail routes with nested structure
	layout("routes/collections/layout.tsx", [
		route("/collections/:id", "routes/collections/detail.tsx"),
		route("/collections/:id/stats", "routes/collections/stats.tsx"),

		// Vocabulary routes
		layout("routes/collections/vocabulary/layout.tsx", [
			route(
				"/collections/:id/vocabulary",
				"routes/collections/vocabulary/index.tsx"
			),
			route(
				"/collections/:id/vocabulary/my-words",
				"routes/collections/vocabulary/my-words.tsx"
			),
			route(
				"/collections/:id/vocabulary/generate",
				"routes/collections/vocabulary/generate.tsx"
			),
			route(
				"/collections/:id/vocabulary/review",
				"routes/collections/vocabulary/review.tsx"
			),
			route(
				"/collections/:id/vocabulary/mcq",
				"routes/collections/vocabulary/mcq.tsx"
			),
		]),

		// Paragraph routes
		layout("routes/collections/paragraph/layout.tsx", [
			route(
				"/collections/:id/paragraph/paragraph-practice",
				"routes/collections/paragraph/paragraph-practice.tsx"
			),
			route(
				"/collections/:id/paragraph/qa-practice",
				"routes/collections/paragraph/qa-practice.tsx"
			),
			route(
				"/collections/:id/paragraph/grammar-practice",
				"routes/collections/paragraph/grammar-practice.tsx"
			),
		]),
	]),
] satisfies RouteConfig;
