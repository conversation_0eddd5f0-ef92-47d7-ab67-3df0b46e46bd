'use client';

import { Translate } from '@/components/ui';
import { BookMarked, Target } from 'lucide-react';
import { FeatureCard } from './feature-card';

export function LearningPathSection() {
	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="home.learning_path" />
			</h2>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<FeatureCard
					icon={BookMarked}
					titleKey="home.curriculum"
					descriptionKey="home.curriculum_description"
				/>
				<FeatureCard
					icon={Target}
					titleKey="home.milestones"
					descriptionKey="home.milestones_description"
				/>
			</div>
		</div>
	);
}