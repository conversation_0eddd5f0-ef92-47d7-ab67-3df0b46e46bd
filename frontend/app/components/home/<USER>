'use client';

import { Translate } from '@/components/ui';
import { BookText, BookOpen } from 'lucide-react';
import { FeatureCard } from './feature-card';

export function ParagraphsSection() {
	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="home.paragraphs" />
			</h2>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<FeatureCard
					icon={BookText}
					titleKey="home.generate_paragraphs"
					descriptionKey="home.generate_paragraphs_description"
				/>
				<FeatureCard
					icon={BookOpen}
					titleKey="home.practice_reading"
					descriptionKey="home.practice_reading_description"
				/>
			</div>
		</div>
	);
}