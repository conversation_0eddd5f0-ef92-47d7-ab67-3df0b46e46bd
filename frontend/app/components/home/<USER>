"use client";

import { But<PERSON>, KeyboardNavigation, Translate } from "@/components/ui";
import { useTranslation } from "@/contexts";
import { motion } from "framer-motion";
import {
	ArrowRight,
	BookOpen,
	FolderHeart,
	<PERSON>rkles,
	Target,
	Zap,
} from "lucide-react";
import { Link } from "react-router";
import { containerVariants, cardVariants } from "./animation-variants";

export function CollectionsSection() {
	const { t } = useTranslation();

	return (
		<motion.div variants={containerVariants} className="space-y-8">
			<div className="text-center space-y-4">
				<h2 className="text-3xl font-bold">
					<Translate text="home.collections_title" />
				</h2>
				<p className="text-muted-foreground text-lg max-w-2xl mx-auto">
					<Translate text="home.collections_description" />
				</p>
			</div>

			<KeyboardNavigation orientation="horizontal">
				<motion.div
					variants={containerVariants}
					className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-4xl mx-auto"
				>
					{/* Main Collections Card */}
					<motion.div variants={cardVariants}>
						<Link to="/collections" className="w-full block">
							<div className="group relative overflow-hidden bg-gradient-to-br from-primary/10 via-primary/5 to-transparent border border-primary/20 rounded-2xl p-8 hover:shadow-2xl transition-all duration-300 hover:scale-105">
								<div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
								<div className="relative z-10 text-center space-y-4">
									<div className="relative">
										<FolderHeart className="size-16 text-primary mx-auto" />
										<motion.div
											animate={{ rotate: 360 }}
											transition={{
												duration: 20,
												repeat: Infinity,
												ease: "linear",
											}}
											className="absolute -top-2 -right-2"
										>
											<Sparkles className="size-6 text-secondary" />
										</motion.div>
									</div>
									<div>
										<h3 className="font-bold text-2xl mb-2">
											<Translate text="home.collections" />
										</h3>
										<p className="text-muted-foreground">
											<Translate text="home.collections_main_description" />
										</p>
									</div>
									<Button
										className="bg-primary hover:bg-primary/90 text-primary-foreground group-hover:scale-105 transition-transform duration-200"
										size="lg"
									>
										<Translate text="home.start_organizing" />
										<ArrowRight className="size-4 ml-2" />
									</Button>
								</div>
							</div>
						</Link>
					</motion.div>

					{/* Features Grid */}
					<motion.div
						variants={cardVariants}
						className="grid grid-cols-1 gap-4"
					>
						<div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 hover:bg-card/70 transition-colors duration-200">
							<div className="flex items-center gap-3 mb-3">
								<Zap className="size-6 text-yellow-500" />
								<h4 className="font-semibold">
									<Translate text="home.generate_words" />
								</h4>
							</div>
							<p className="text-sm text-muted-foreground">
								<Translate text="home.generate_words_description" />
							</p>
						</div>

						<div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 hover:bg-card/70 transition-colors duration-200">
							<div className="flex items-center gap-3 mb-3">
								<Target className="size-6 text-green-500" />
								<h4 className="font-semibold">
									<Translate text="home.practice_modes" />
								</h4>
							</div>
							<p className="text-sm text-muted-foreground">
								<Translate text="home.practice_modes_description" />
							</p>
						</div>

						<div className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-6 hover:bg-card/70 transition-colors duration-200">
							<div className="flex items-center gap-3 mb-3">
								<BookOpen className="size-6 text-blue-500" />
								<h4 className="font-semibold">
									<Translate text="home.track_progress" />
								</h4>
							</div>
							<p className="text-sm text-muted-foreground">
								<Translate text="home.track_progress_description" />
							</p>
						</div>
					</motion.div>
				</motion.div>
			</KeyboardNavigation>
		</motion.div>
	);
}
