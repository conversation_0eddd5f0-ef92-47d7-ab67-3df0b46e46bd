'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui';
import { Paragraph } from '@prisma/client';

interface ParagraphCardProps {
	paragraph: Paragraph & { _count?: { questions?: number } }; // Allow _count for questions
	onViewDetails?: (paragraph: Paragraph) => void;
}

export function ParagraphCard({ paragraph, onViewDetails }: ParagraphCardProps) {
	const wordCount = paragraph.content.split(/\s+/).filter(Boolean).length;
	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle className="flex items-center justify-between">
					<span>Đoạn văn</span>
					<Badge variant="outline">{paragraph.language}</Badge>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					<p className="text-sm line-clamp-3">{paragraph.content}</p>
					<div className="flex items-center justify-between text-sm text-muted-foreground">
						<span>{wordCount} từ</span>
						<span>{paragraph._count?.questions ?? 0} câu hỏi</span>
					</div>
					<Button
						variant="outline"
						onClick={() => onViewDetails?.(paragraph)}
						className="w-full"
					>
						Xem chi tiết
					</Button>
				</div>
			</CardContent>
		</Card>
	);
}
