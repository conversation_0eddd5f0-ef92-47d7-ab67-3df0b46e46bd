'use client';

import { cn } from '@/lib';
import { forwardRef } from 'react';

export const ScreenReaderAnnouncement = forwardRef<
	HTMLDivElement,
	{
		message: string;
		priority?: 'polite' | 'assertive';
		className?: string;
	}
>(({ message, priority = 'polite', className }, ref) => {
	return (
		<div ref={ref} className={cn('sr-only', className)} aria-live={priority} aria-atomic="true">
			{message}
		</div>
	);
});

ScreenReaderAnnouncement.displayName = 'ScreenReaderAnnouncement';
