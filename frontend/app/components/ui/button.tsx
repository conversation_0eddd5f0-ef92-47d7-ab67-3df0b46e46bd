'use client';

import { cn } from '@/lib';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';
import React from 'react';

const buttonVariants = cva(
	"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive active:scale-[0.98] active:opacity-90",
	{
		variants: {
			variant: {
				default:
					'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 active:bg-primary/80',
				destructive:
					'bg-destructive text-white shadow-xs hover:bg-destructive/90 active:bg-destructive/80 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
				outline:
					'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground active:bg-accent/80 dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
				secondary:
					'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80 active:bg-secondary/70',
				ghost: 'hover:bg-accent hover:text-accent-foreground active:bg-accent/80 dark:hover:bg-accent/50',
				link: 'text-primary underline-offset-4 hover:underline active:text-primary/80',
			},
			size: {
				default: 'min-h-[44px] min-w-[44px] px-4 py-2 has-[>svg]:px-3',
				sm: 'min-h-[44px] min-w-[44px] rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
				lg: 'min-h-[44px] min-w-[44px] rounded-md px-6 has-[>svg]:px-4',
				icon: 'size-[44px]',
			},
		},
		defaultVariants: {
			variant: 'default',
			size: 'default',
		},
	}
);

function Button({
	className,
	variant,
	size,
	asChild = false,
	'aria-label': ariaLabel,
	children,
	loading = false,
	...props
}: React.ComponentProps<'button'> &
	VariantProps<typeof buttonVariants> & {
		asChild?: boolean;
		'aria-label'?: string;
		loading?: boolean;
	}) {
	const Comp = asChild ? Slot : 'button';
	const buttonId = React.useId();

	// Generate aria-label if not provided and children is a string
	const defaultAriaLabel = typeof children === 'string' ? children : undefined;

	// Handle keyboard events for better accessibility
	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			if (props.onClick) {
				props.onClick(e as any);
			}
		}
	};

	return (
		<Comp
			data-slot="button"
			className={cn(buttonVariants({ variant, size, className }))}
			role="button"
			aria-label={ariaLabel || defaultAriaLabel}
			aria-disabled={props.disabled || loading}
			aria-pressed={props['aria-pressed']}
			aria-expanded={props['aria-expanded']}
			aria-haspopup={props['aria-haspopup']}
			aria-controls={props['aria-controls']}
			tabIndex={props.disabled || loading ? -1 : 0}
			onKeyDown={handleKeyDown}
			id={buttonId}
			disabled={props.disabled || loading}
			{...props}
		>
			{loading ? (
				<>
					<Loader2 className="h-4 w-4 animate-spin text-foreground" />
					Loading...
				</>
			) : (
				children
			)}
			{props.disabled && (
				<span className="sr-only" aria-live="polite">
					Disabled
				</span>
			)}
		</Comp>
	);
}

export { Button, buttonVariants };
