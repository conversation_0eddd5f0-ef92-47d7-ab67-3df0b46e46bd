'use client';

import {
	Button,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui';
import { cn } from '@/lib';
import { Menu, Plus, X } from 'lucide-react';
import { useState } from 'react';

interface FloatButtonProps {
	items: {
		label: string;
		icon: React.ReactNode;
		onClick: () => void;
	}[];
	className?: string;
}

export function FloatButton({ items, className }: FloatButtonProps) {
	const [open, setOpen] = useState(false);

	return (
		<div className={cn('fixed bottom-6 right-6 z-50 flex flex-col items-end gap-2', className)}>
			{open && (
				<div className="flex flex-col gap-2 mb-2">
					{items.map((item, index) => (
						<Button
							key={index}
							size="icon"
							variant="outline"
							className="bg-background shadow-md h-12 w-12 rounded-full"
							onClick={() => {
								setOpen(false);
								item.onClick();
							}}
						>
							{item.icon}
							<span className="sr-only">{item.label}</span>
						</Button>
					))}
				</div>
			)}

			<Button
				size="icon"
				className="h-14 w-14 rounded-full shadow-lg"
				onClick={() => setOpen(!open)}
			>
				{open ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
			</Button>
		</div>
	);
}

export function FloatMenuButton({ items, className }: FloatButtonProps) {
	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					size="icon"
					className={cn(
						'h-14 w-14 rounded-full shadow-lg fixed bottom-6 right-6 z-50',
						className
					)}
				>
					<Plus className="h-6 w-6" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="mb-1 mr-1">
				{items.map((item, index) => (
					<DropdownMenuItem key={index} onClick={item.onClick} className="cursor-pointer">
						<div className="flex items-center gap-2">
							{item.icon}
							<span>{item.label}</span>
						</div>
					</DropdownMenuItem>
				))}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
