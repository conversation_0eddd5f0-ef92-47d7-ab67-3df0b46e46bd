'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui';
import { Language } from '@prisma/client';

interface LanguageSelectProps {
	value?: Language;
	onChange: (value: Language) => void;
}

// Translation dictionary for language names in each language
const languageNames: Record<Language, Record<string, string>> = {
	EN: {
		placeholder: 'Select language',
		EN: 'English',
		VI: 'Vietnamese',
	},
	VI: {
		placeholder: 'Chọn ngôn ngữ',
		EN: 'Tiếng Anh',
		VI: 'Tiếng Việt',
	},
};

export function LanguageSelect({ value = 'EN', onChange }: LanguageSelectProps) {
	// Get translations for the currently selected language
	const translations = languageNames[value];

	return (
		<Select value={value} onValueChange={onChange}>
			<SelectTrigger className="w-[180px]">
				<SelectValue placeholder={translations.placeholder} />
			</SelectTrigger>
			<SelectContent>
				<SelectItem value="EN">{translations.EN}</SelectItem>
				<SelectItem value="VI">{translations.VI}</SelectItem>
			</SelectContent>
		</Select>
	);
}
