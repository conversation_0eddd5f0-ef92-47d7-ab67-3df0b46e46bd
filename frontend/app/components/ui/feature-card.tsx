"use client";

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Translate,
} from "@/components/ui";
import { LucideIcon, ArrowRight, ExternalLink } from "lucide-react";
import { Link } from "react-router";

interface SubFeature {
	titleKey: string;
	icon: LucideIcon;
	subLink: string;
}

interface FeatureCardProps {
	titleKey: string;
	descriptionKey: string;
	icon: LucideIcon;
	link: string;
	subFeatures?: SubFeature[];
}

export function FeatureCard({
	titleKey,
	descriptionKey,
	icon: Icon,
	link,
	subFeatures,
}: FeatureCardProps) {
	return (
		<Card className="group relative overflow-hidden border-border/50 bg-gradient-to-br from-background to-muted/20 hover:shadow-xl hover:shadow-primary/5 transition-all duration-300 hover:border-primary/30 flex flex-col h-full">
			{/* Background decoration */}
			<div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
			<div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-secondary/10 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

			<CardHeader className="relative pb-4">
				<div className="flex items-center justify-between mb-3">
					<div className="p-3 rounded-xl bg-primary/10 border border-primary/20 group-hover:bg-primary/20 group-hover:border-primary/30 transition-all duration-300">
						<Icon className="h-6 w-6 text-primary transition-transform duration-300" />
					</div>
					<ExternalLink className="h-4 w-4 text-muted-foreground/50 group-hover:text-primary group-hover:translate-x-1 group-hover:-translate-y-1 transition-all duration-300" />
				</div>
				<CardTitle className="text-xl font-bold group-hover:text-primary transition-colors duration-300">
					<Translate text={titleKey} />
				</CardTitle>
				<p className="text-sm text-muted-foreground leading-relaxed">
					<Translate text={descriptionKey} />
				</p>
			</CardHeader>

			<CardContent className="relative flex-grow flex flex-col">
				{subFeatures && subFeatures.length > 0 ? (
					<>
						<div className="flex flex-col gap-2 flex-grow">
							{subFeatures.map((sub, index) => (
								<Link
									key={sub.titleKey}
									href={`${link}${sub.subLink}`}
									passHref
								>
									<div
										className="group/item flex items-center gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 border border-transparent hover:border-primary/20 transition-all duration-200 cursor-pointer"
										style={{ animationDelay: `${index * 50}ms` }}
									>
										<div className="p-1.5 rounded-md bg-background border border-border/50 group-hover/item:border-primary/30 group-hover/item:bg-primary/5 transition-all duration-200">
											<sub.icon className="h-4 w-4 text-muted-foreground group-hover/item:text-primary transition-colors duration-200" />
										</div>
										<span className="text-sm font-medium flex-1 group-hover/item:text-primary transition-colors duration-200">
											<Translate text={sub.titleKey} />
										</span>
										<ArrowRight className="h-4 w-4 text-muted-foreground/50 group-hover/item:text-primary group-hover/item:translate-x-1 transition-all duration-200" />
									</div>
								</Link>
							))}
						</div>
					</>
				) : (
					<div className="mt-auto">
						<Link href={link} passHref>
							<Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground group-hover:shadow-lg group-hover:shadow-primary/25 transition-all duration-300">
								<Translate text="collections.overview.go_to_section" />
								<ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
							</Button>
						</Link>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
