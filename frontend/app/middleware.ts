import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware } from './backend/middleware/auth.middleware';

// Ensure middleware only runs on the server side
export const config = {
	matcher: [],
	runtime: 'nodejs',
};

export async function middleware(request: NextRequest) {
	// Skip authentication for public endpoints
	const publicPaths = ['/api/auth/telegram-login', '/api/auth/logout'];

	// Only apply auth middleware for URLs starting with /api
	// except for the public endpoints
	if (
		request.nextUrl.pathname.startsWith('/api') &&
		!publicPaths.some((path) => request.nextUrl.pathname.startsWith(path))
	) {
		const result = await authMiddleware(request);

		// If result is a Response, it means there was an error
		if (result instanceof Response) {
			return result;
		}
	}

	const response = NextResponse.next({
		request: {
			// New request headers
			headers: request.headers,
		},
	});

	return response;
}
