import { Provider } from "@prisma/client";

const env = process.env;

interface ServerConfig {
	port: number;
	env: string;
}

interface AuthConfig {
	jwtCookieName: string;
	jwtSecret: string;
	jwtExpiresIn: number;
	defaultUser: {
		provider: Provider;
		provider_id: string;
	};
}

interface LLMConfig {
	openAIKey: string;
	openAIModel: string;
	maxExamples: number;
	temperature: number;
	maxTokens: number;
}

export class Config {
	private static instance: Config;
	private serverConfig: ServerConfig;
	private authConfig: AuthConfig;
	private llmConfig: LLMConfig;

	private constructor() {
		// Initialize all configurations in constructor
		this.serverConfig = {
			port: Number.parseInt(env.PORT || "5000", 10),
			env: env.NODE_ENV || "development",
		};

		this.authConfig = {
			jwtCookieName: env.JWT_COOKIE_NAME || "auth_token",
			jwtSecret: env.JWT_SECRET || "your-secret-key",
			jwtExpiresIn: Number(env.JWT_EXPIRES_IN) || 30 * 24 * 60 * 60,
			// Default user for development environment
			defaultUser: {
				provider: Provider.TELEGRAM,
				provider_id: "711352287",
			},
		};

		this.llmConfig = {
			openAIKey: env.LLM_OPENAI_API_KEY || "",
			openAIModel: env.LLM_OPENAI_MODEL || "gpt-4o-mini",
			maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || "8"),
			temperature: Number.parseFloat(env.LLM_TEMPERATURE || "0.7"),
			maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || "1000"),
		};
	}

	public static getInstance(): Config {
		if (!Config.instance) {
			Config.instance = new Config();
		}
		return Config.instance;
	}

	// Server configuration
	public getServerConfig(): ServerConfig {
		return this.serverConfig;
	}

	// Authentication configuration
	public getAuthConfig(): AuthConfig {
		return this.authConfig;
	}

	// LLM configuration
	public getLLMConfig(): LLMConfig {
		return this.llmConfig;
	}
}

// Helper functions for backward compatibility
export function getServerConfig(): ServerConfig {
	return Config.getInstance().getServerConfig();
}

export function getAuthConfig(): AuthConfig {
	return Config.getInstance().getAuthConfig();
}

export function getLLMConfig(): LLMConfig {
	return Config.getInstance().getLLMConfig();
}
