{"name": "frontend", "packageManager": "yarn@4.9.2", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^2.4.6", "@types/nprogress": "^0.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.6.2", "isbot": "^5.1.27", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.483.0", "node-cache": "^5.1.2", "nprogress": "^0.2.0", "radix-ui": "^1.1.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-router": "^7.5.3", "react-scan": "^0.3.4", "react-window": "^1.8.11", "recharts": "^2.15.3", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.29.0", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}